{"_commit_hash": null, "auto_map": {"AutoConfig": "configuration_keye.KeyeConfig", "AutoModel": "modeling_keye.KeyeForConditionalGeneration", "AutoModelForCausalLM": "modeling_keye.KeyeForConditionalGeneration"}, "architectures": ["KeyeForConditionalGeneration"], "attention_bias": false, "attention_dropout": 0.0, "bos_token_id": 151643, "eos_token_id": 151645, "vision_start_token_id": 151652, "vision_end_token_id": 151653, "vision_token_id": 151654, "image_token_id": 151655, "video_token_id": 151656, "head_dim": 128, "hidden_act": "silu", "hidden_size": 4096, "initializer_range": 0.02, "intermediate_size": 12288, "max_position_embeddings": 40960, "max_window_layers": 36, "model_type": "<PERSON><PERSON>", "num_attention_heads": 32, "num_hidden_layers": 36, "num_key_value_heads": 8, "rms_norm_eps": 1e-06, "rope_theta": 1000000, "sliding_window": null, "tie_word_embeddings": false, "torch_dtype": "bfloat16", "transformers_version": "4.41.2", "use_cache": true, "use_sliding_window": false, "initializer_factor": 1.0, "vision_config": {"_attn_implementation_autoset": true, "add_cross_attention": false, "architectures": ["SiglipVisionModel"], "attention_dropout": 0.0, "auto_map": {"AutoConfig": "configuration_keye.KeyeVisionConfig", "AutoModel": "modeling_keye.SiglipVisionModel"}, "hidden_size": 1152, "image_size": 384, "intermediate_size": 4304, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_hidden_layers": 27, "patch_size": 14, "spatial_merge_size": 2, "tokens_per_second": 2, "temporal_patch_size": 2}, "rope_scaling": {"type": "mrope", "mrope_section": [16, 24, 24]}, "vocab_size": 151936}