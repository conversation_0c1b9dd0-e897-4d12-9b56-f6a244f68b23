[project]
name = "keye-vl-utils"
version = "1.0.0"
description = "Keye Vision Language Model Utils - PyTorch"
authors = [
    { name = "Kwai Keye Team"},
]
dependencies = [
    "requests",
    "pillow",
    "av",
    "packaging",
]
readme = "README.md"
requires-python = ">= 3.8"
license = {text = "Apache-2.0"}
keywords = [
    'large language model',
    'vision language model',
    'keye-vl',
    'pytorch',
]
classifiers = [
    'Development Status :: 4 - Beta',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'Programming Language :: Python :: 3',
    'License :: OSI Approved :: Apache Software License',
]

[project.urls]
Homepage = "https://github.com/Kwai-Keye/Keye/tree/main/keye-vl-utils"
Repository = "https://github.com/Kwai-Keye/Keye.git"
Issues = "https://github.com/Kwai-Keye/Keye/issues"

[project.optional-dependencies]
decord = [
    "decord",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = [
    "torch",
    "torchvision",
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/keye_vl_utils"]

[tool.ruff]
line-length = 119

[tool.ruff.lint]
ignore = ["C408", "C901", "E501", "E731", "E741", "W605"]
select = ["C", "E", "F", "I", "W"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["E402", "F401", "F403", "F811"]

[tool.ruff.lint.isort]
lines-after-imports = 2
known-first-party = ["keye_vl_utils"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
