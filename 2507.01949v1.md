\title{
Kwai Keye-VL Technical Report
}

\author{
Keye Team, Kuaishou Group \\ https://kwai-keye.github.io/
https://huggingface.co/<PERSON>wai-Keye
https://github.com/Kwai-Keye/Keye
}

\begin{abstract}
While Multimodal Large Language Models (MLLMs) demonstrate remarkable capabilities on static images, they often fall short in comprehending dynamic, information-dense short-form videos, a dominant medium in today's digital landscape. To bridge this gap, we introduce Kwai Keye-VL, an 8-billion-parameter multimodal foundation model engineered for leading-edge performance in short-video understanding while maintaining robust general-purpose vision-language abilities. The development of Keye-VL rests on two core pillars: a massive, high-quality dataset exceeding 600 billion tokens with a strong emphasis on video, and an innovative training recipe. This recipe features a fourstage pre-training process for solid vision-language alignment, followed by a meticulous two-phase post-training process. The first post-training stage enhances foundational capabilities like instruction following, while the second phase focuses on stimulating advanced reasoning. In this second phase, a key innovation is our five-mode "cold-start" data mixture, which includes "thinking", "non-thinking", "auto-think", "think with image", and high-quality video data. This mixture teaches the model to decide when and how to reason. Subsequent reinforcement learning (RL) and alignment steps further enhance these reasoning capabilities and correct abnormal model behaviors, such as repetitive outputs. To validate our approach, we conduct extensive evaluations, showing that Keye-VL achieves state-of-the-art results on public video benchmarks and remains highly competitive on general image-based tasks (Figure 1). Furthermore, we develop and release the KC-MMBench, a new benchmark tailored for real-world short-video scenarios, where Keye-VL shows a significant advantage. Comprehensive human evaluations also confirm that our model provides a superior user experience compared to other leading models of a similar scale. This paper details the architecture, data construction strategy, and training methodology of Keye-VL, offering valuable insights for building the next generation of MLLMs for the video era.
\end{abstract}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-01.jpg?height=623&width=1597&top_left_y=1699&top_left_x=235)

Figure 1: Benchmark performance of Kwai Keye-VL: Keye-VL-8B establishes a new state-of-the-art among models of a similar scale, showing a clear lead in video-centric benchmarks (left) while maintaining competitive results on general perception and reasoning tasks (right), validating our training approach.

\section*{Contents}
1 Introduction ..... 4
2 Model Architecture ..... 5
2.1 Vision Encoder with Native-Resolution ..... 6
2.2 Visual Encoding ..... 6
3 Pre-Training ..... 6
3.1 Data Pipeline ..... 6
3.1.1 Image Caption Data ..... 7
3.1.2 OCR \&VQA Data ..... 7
3.1.3 Grounding \& Counting Data ..... 7
3.1.4 Interleaved Text-Image Data ..... 7
3.1.5 Video Data ..... 8
3.2 Training Recipe ..... 9
4 Post-Training ..... 9
4.1 No-Reasoning Training: Establishing Foundational Performance ..... 10
4.1.1 Step I.1: Supervised Fine-Tuning ..... 11
4.1.2 Step I.2: Mixed Preference Optimization ..... 11
4.2 Reasoning Training: Core Breakthrough for Complex Cognition ..... 11
4.2.1 Step II.1: CoT Cold-Start ..... 11
4.2.2 Step II.2: Mix-Mode RL ..... 12
4.2.3 Step II.3: Iterative Alignment ..... 13
5 Training Infrastructure ..... 13
6 Evaluation ..... 14
6.1 Zero-shot Image Classification of ViT ..... 14
6.2 Public Benchmarks ..... 14
6.3 Internal Benchmarks ..... 16
6.3.1 Design Strategies and Core Principles ..... 16
6.3.2 Evaluation Metrics and Baselines ..... 19
6.3.3 Evaluation Results ..... 19
6.3.4 Analysis of Kwai Keye-VL's Limitations ..... 20
6.4 Quantitative Results ..... 21
7 Discussion ..... 21
7.1 Mutual Enhancement between Reasoning and Non-Reasoning Data ..... 21
7.2 Performance Gain from RL Training ..... 21
7.3 Analysis about Auto-Think Mode ..... 21
8 Conclusion and Future Work ..... 22
A Strategies for Data Decontamination ..... 29
A. 1 Pre-training ..... 29
A. 2 Post Training ..... 29
B Construction of KC-MMbench ..... 29
C Case Study ..... 33
C. 1 Modality ..... 33
C.1.1 Pure Text Case ..... 33
C.1.2 Image Cases ..... 34
C.1.3 Video Cases ..... 36
C. 2 Thinking Mode ..... 38
C.2.1 Agentic Thinking Case ..... 38
C.2.2 Auto Thinking Cases ..... 39
D Authors (Alphabetical order) ..... 41

\section*{1 Introduction}

In recent years, Large Language Models (LLMs) advance rapidly (Grattafiori et al. (2024); Abdin et al. (2024); Team (2025b); Wang et al. (2024c)), ushering in a new era of artificial intelligence with their powerful capabilities in understanding (FaceBook (2025); Team (2025a)), generating (Yang et al. (2025); Seed et al. (2025)), and reasoning with language (Guo et al. (2025a); Liu et al. (2024a)). This wave also propels the swift progress of Multimodal Large Language Models ( OpenAI (2025); Chen et al. (2024b;c); Hurst et al. (2024); Team et al. (2025b); Feng et al. (2024); Fu et al. (2025b); Han et al. (2024); Li et al. (2023); Luo et al. (2023); Guo et al. (2025b); Zhang et al. (2024b); Wu et al. (2024b)) (MLLMs), which extend these formidable language abilities to the visual domain, enabling them to perform complex tasks such as visual question answering (Li et al. (2024); Chen et al. (2024d)), detailed image captioning (Luo et al. (2024a); Rang et al. (2025); Li et al. (2025a)), object grounding (Bai et al. (2025); Ma et al. (2025)) and visual reasoning (OpenAI (2025); Su et al. (2025); Hu et al. (2025a)).
Despite significant progress in understanding static images, a major challenge remains in comprehending video content (Shen et al. (2025); Lin et al. (2023)), which is more dynamic and information-dense (Luo et al. (2024c); Team et al. (2025a)). Short-form videos, in particular, become the primary medium for communication, entertainment, and commerce on platforms like Kuaishou( Zhou et al. (2025); Lu et al. (2025)). Understanding short videos is far more complex than recognizing individual objects (Li et al. (2024)); it requires a model to deeply comprehend the sequence of events, causal relationships, and the overall narrative. Furthermore, the model must integrate information from multiple sources, including video frames and audio (converted to text via Automatic Speech Recognition). Most existing multimodal models, primarily designed for handling combinations of single images and text, lack deep exploration for video tasks and thus often fail to capture the rich, contextual, and sequential information present in videos. To address this critical gap, we introduce Kwai Keye-VL, a meticulously engineered 8-billion-parameter multimodal foundation model. It achieves leading-edge performance in short-video understanding while also maintaining robust capabilities in general-purpose vision-language tasks (as shown in Figure 1). Our work is driven by a pressing need for a model that not only "sees" the world but also "thinks" about its dynamic patterns. This is crucial for enhancing user experience and enabling more intelligent applications in content creation, recommendation, and e-commerce on video-centric platforms.

The development of Kwai Keye-VL rests upon several core techniques. First, we construct a largescale and diverse dataset exceeding 600 billion tokens, with a special focus on high-quality video data. This data undergoes a rigorous processing pipeline, including filtering, re-captioning with advanced models to generate more precise descriptions, and frame-level annotation to ensure quality. Second, we design an innovative training methodology, which includes a four-stage pre-training process to build a solid foundation for vision-language alignment. Following pre-training, we further enhance Keye-VL's capabilities through a two-phase post-training process:
$\diamond$ Stage 1: optimizing foundational capabilities: We focus on improving the model's basic performance in areas like instruction following. This is achieved through supervised fine-tuning (SFT) and mixed preference optimization (MPO) on high-quality data.
$\diamond$ Stage 2: stimulating and enhancing reasoning abilities: We begin by creating high-quality "cold-start" data containing five modes: conventional question-answering, long chain-of-thought, auto-reasoning decision, "think with an image" (e.g., generating code to process images), and high-quality video data. We train the model on a mix of these modes, teaching it to select the most appropriate response style. This allows it to think deeply for complex reasoning tasks while responding quickly to simple ones. Subsequently, we employ reinforcement learning to further strengthen its complex reasoning skills. Finally, we use the MPO algorithm for several rounds of iterative alignment to correct issues such as repetitive outputs and flawed logical reasoning.

Throughout both pre-training and post-training, we perform rigorous data de-duplication. We compare our training data against general benchmark samples and remove those with high similarity. This process also reveals several public datasets currently implicated in significant data leakage, which we argue should be avoided in model training.
To validate our approach's effectiveness, we first conduct extensive evaluations on multiple public benchmarks. As Figure 1 shows, Keye-VL's performance is highly competitive in general image understanding and reasoning, even reaching state-of-the-art level. In video understanding, Keye-VL-8B substantially outperforms previous state-of-the-art models across several benchmarks. To specifically address short-video understanding, we also develop and open-source the Kuaishou Community Multimodal Benchmark (KC-MMBench) and a comprehensive internal evaluation suite to rigorously assess the model's capabilities in realistic, video-centric commercial application scenarios. On this benchmark, Keye-VL-8B also demonstrates a significant advantage, highlighting its value for commercial applications.
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-05.jpg?height=1161&width=1603&top_left_y=242&top_left_x=235)

Figure 2: The Kwai Keye-VL model architecture is based on the Qwen3-8B language model and incorporates a vision encoder initialized from the open-source SigLIP. It supports native dynamic resolution, preserving the original aspect ratio of images by dividing each into a $14 \times 14$ patch sequence. A simple MLP layer then maps and merges the visual tokens. The model uses 3D RoPE for unified processing of text, image, and video information, establishing a one-to-one correspondence between position encoding and absolute time to ensure precise perception of temporal changes in video information.

For a more granular assessment of Keye-VL's capabilities across various aspects and its real-world user experience, we construct an additional fine-grained internal benchmark. On this benchmark, we conduct a comprehensive and detailed human evaluation of similarly-sized models, including Keye-VL-8B, Qwen2.5-VL-7B, InternVL3-8B, and MiMo-VL-7B. The evaluation results indicate that our model delivers a superior user experience on both video and image-text tasks compared to these baselines. Concurrently, we present an in-depth analysis of Keye-VL's current limitations in fine-grained perception, temporal understanding, and high-level reasoning, outlining future directions for development.

In summary, this paper provides a detailed account of the Kwai Keye-VL's architecture, data processing pipeline, training methodology, and comprehensive evaluation results, offering valuable insights for building the next generation of Multimodal Large Language Models for the video era.

\section*{2 Model Architecture}

Figure 2 gives a high-level overview of our Keye-VL, which follows a classic MLLM architecture that includes three key components: a Vision Transformer (ViT), a MLP projector, and a language decoder. For ViT component, we apply the open-source SigLIP-400M-384-14 ${ }^{1}$ as our vision encoder to extract vision information. For LLM component, we employ the widely used Qwen3-8B as our language decoder, to provide the universal world semantic knowledge understanding capabilities. For the projector, we randomly initialize its parameters and fully pre-training it at the Stage 1. In the following sections, we provide our key upgrades, data pipeline and training recipes.

\footnotetext{
${ }^{1}$ https://huggingface.co/google/siglip-so400m-patch14-384
}

\subsection*{2.1 Vision Encoder with Native-Resolution}

In past years, many MLLMs efforts have adopted the well-trained fixed-resolution ViTs as their vision encoders, such as ViT-bigG (Cherti et al. (2023) ), SigLIP-400M (Zhai et al. (2023)) and others. However, unlike pre-trained CLIP-based ViTs (Radford et al. (2021) ) that only handle coarse-grained image-caption matching task during training, MLLMs often tackle various finer-grained generation tasks, existing a large gap between them. Therefore, we anticipate that our ViT will possess the following capabilities: during processing, images and videos maintain their structural integrity and all details are preserved.

To this end, there are some pioneer MLLMs exploring native-resolution ViT in recent years, such as Qwen2.5-VL, Seed-VL-1.5, Kimi-VL, etc. In Keye-VL, we also implement a native-resolution ViT, to naturally process images at original resolution, avoiding some complex and redundant image splicing/splitting operations (e.g., MiniCPM2 (Yao et al. (2024))). Specifically, our ViT is initialized by the SigLIP-400M-384-14, a fixed-resolution variant with absolute learnable position embeddings to inject the spatial information. According to it, we first employ interpolation techniques to extend fixedlength learnable position embeddings into resolution-adaptive position embeddings, enabling our basic native-resolution modeling while preserving the pretrained workflow. Afterwards, to further enhance extrapolation capabilities for positional encoding along visual dimensions, we introduce 2D Rotary Position Embedding (RoPE) to strengthen the visual information modeling. In our trial experience, we observe that incorporating 2D RoPE significantly improves the model's performance on high-resolution image. Finally, building upon the two types of position embeddings, we incorporate the NaViT packing with FlashAttention techniques to continue training our ViT across images with varying resolutions.

During the ViT pre-training procedure, we optimize our native-resolution modifications via SigLIP loss function (the text tower is also from SigLIP-400M-384-14). We use the same distribution data as the downstream MLLM for training, including a total of 500B Tokens from open source data DataComp (Gadre et al. (2023)), LAION (Schuhmann et al. (2022)), CC12M (Changpinyo et al. (2021)), PD12M (Meyer et al. (2024)), COCO (Lin et al. (2014)) and other in-house data.

\subsection*{2.2 Visual Encoding}

To guarantee that our language decoder can perceive enough visual signals to understand images and videos in detail, we leave sufficient token buffer for image and videos modeling.

For images of different resolutions, we set the total number of tokens for each image to 16384, which can cover images with more than one million pixels and is sufficient to help the model to see the details of the image in most scenarios. For video modeling, we devise a dynamic resolution strategy that balances the maximum number of frames and the total number of tokens. In Keye-VL, we currently set the min/max token number per frame as $128 / 768$, and the max vision token as 24576 , this setting can automatically make trade-off between the breadth and depth of visual perception. Subsequently, based on the extracted frames, we re-calculate the FPS and ensure strict alignment in the time position in 3D RoPE dimensions during training (position +1 corresponds to +0.5 second in real world). Meanwhile, we are exploring other more efficient frame modeling techniques to ensure that more frames could feed to our LLM with acceptable computation.

\section*{3 Pre-Training}

In this section, we first describe the construction of the pre-training dataset, followed by an overview of the overall training pipeline and configuration.

\subsection*{3.1 Data Pipeline}

In our data construction pipeline, we have assembled a diverse, high-quality corpus with exceeding 600 billion tokens to support our models training, sourced from both public datasets and proprietary in-house data. Generally, our training data encompasses six primary categories: Image Caption, OCR \& VQA, Grounding \& Counting, Interleaved, Video Understanding and Pure Text data. To ensure these overall data quality, we have designed customized filtering mechanisms tailored to the characteristics of each data category. For large volumes of medium-quality data, we employ CLIP (Radford et al. (2021)) scores for preliminary filtering. For smaller amounts of high-quality data, we utilize open-source MLLMs as discriminators for data selection. Additionally, we also conduct rigorous image-based deduplication operation, to avoid the potential data leakage between our training corpus and evaluation benchmarks (Dixit et al. (2021)). Specifically, we identify highly similar images, then remove these near-duplicates
from the dataset. We detail and list a part of deduplication results in Table 8. In the following sections, we provide detailed descriptions of each category of training data.

\subsection*{3.1.1 Image Caption Data}

Image caption task provides the fundamental world knowledge to establish a mapping relationship between visual features and linguistic concepts by pairing image with textual descriptions. Based on large-scale caption data, our model gains the ability to perceive and comprehend a broad, rich spectrum of world knowledge, such as real-world physical principles and cultural conventions. Although we can public access many diverse Chinese and English open-source caption data source, such as LAION (Schuhmann et al. (2022)), DataComp (Gadre et al. (2023)) and Coyo (Byeon et al. (2022)), the quality of such data is often unreliable, as it typically only undergoes simple crawler-based matching.

To alleviate such data noise, we conduct strict similarity-based filtering pipeline to control the data quality, e.g., scoring the raw rigorous image-caption pair by a CLIP model. In practice, to ensure data quality, we retain high-similarity image-caption pairs (e.g., CLIP score $>0.9$ ) while leveraging filtered low-quality open-source image data and our in-house image data through a re-captioning pipeline. During the re-caption, we utilize several MLLMs (Qwen2.5-VL 72B (Bai et al. (2025)), Tarsier2 (Yuan et al. (2025)), GPT-4o (Hurst et al. (2024)), Gemini1.5-pro (Team et al. (2023)) and others) to generate the synthesis caption for vary resolution images and image category information. In our experience, we find that recaption data generated by different MLLMs can be very helpful for fine-grained image understanding.

\subsection*{3.1.2 OCR \& VQA Data}

Optical Character Recognition (OCR) and Visual Question Answering (VQA) are vital tasks to encourage our model to distinguish the details of images. By integrating OCR capabilities, the model can accurately extract and interpret textual information within images, while VQA task enables our model to comprehend and reason about visual content in a context-aware manner. In order to build our capabilities in OCR and VQA, we have collected a large number of open-source data, such as Latex-Formula, hand-write text, real-world street views, charts, rich-text documents, multi-image OCR and so on. Since most of the open-source datasets are in English, to further enhance the model's capability in Chinese OCR \& VQA tasks, we introduce multiple techniques for synthesizing in-house Chinese data:
$\diamond$ Synthesis: To enhance the model's OCR capabilities, we aggregate both open-source and in-house image-text datasets, utilizing the text-dense images to build a comprehensive OCR dataset which covering diverse scenario. For VQA task, we first design a set of seed-questions and expand the initial question pool through self-evolution methods. Next, both images and their corresponding captions are fed into MLLMs to generate high-quality and diverse VQA data.
$\diamond$ Rendering: Considering the scarcity of high-quality open-source Chinese OCR data, we further leverage font rendering tools to synthesize high-quality OCR samples (includes (1) diverse image backgrounds/layout, (2) semantic/non-semantic text, (3) multiple fonts styles/sizes and (4) vary image resolutions), which significantly enhances the model's robustness for Chinese OCR recognition.

\subsection*{3.1.3 Grounding \& Counting Data}

Object grounding is one of the fundamental abilities of MLLMs( Bai et al. (2025); Seed et al. (2025)), which enables our model to establish a direct connection between visual information and text semantics. In Keye-VL training, we primarily utilize three object localization forms: center points, bounding boxes, and polygons. Their coordinates are strictly typed as integers and normalized to the range $[0,1000)$ for different resolution images, as shown in the Table 1. In general, we mainly employ the RefCoCo (Kazemzadeh et al. (2014)), VisualGenome (Krishna et al. (2017)), TolokaVQA (Ustalov et al. (2023)) as our grounding data source, and the PixMo (Deitke et al. (2024)) as our counting data source. To filter the incorrect, missing, or ambiguous annotation grounding data, we utilize the CLIP to select the higher-score points/boxes/polygons as our training data, i.e., extracting the corresponding grounding area from the image to compute its similarity with the target objective text.

\subsection*{3.1.4 Interleaved Text-Image Data}

Instead of the learning task surrounding the single images, we also introduce a large amount of interleaved data to enhance our language decoder's longer multi-modal context modeling ability. Actually, beyond modeling multi-image correlations, the interleaved data could contribute several critical advantages in pre-training: (1) Preservation of General Knowledge: It contains a wealth of universal knowledge, ensuring that the LLM module's core capabilities are not degraded during training, (2) Enhanced VisionLanguage Alignment: By leveraging in-context learning, it helps the model better align visual and
\begin{tabular}{|l|l|}
\hline & center points \\
\hline Example Description & < | point_start | > [[x1, y1] ] < point_end | > The $[x 1, y 1]$ is the center point of queried objective. \\
\hline Example Description & < | point_start | > [[x1, y1], [x2, y2]] < | point_end | > Supporting multiple points for a single queried objective. \\
\hline Example Description & <| object_ref_start | >obj< | object_ref_end | >< | point_start | > [[x1, y1] < | point_end | > The $[\mathrm{x} 1, \mathrm{y} 1]$ is the center point of 'obj'. \\
\hline \multicolumn{2}{|c|}{bounding boxes} \\
\hline Example Description & < | box_start | >[[x1, y1, x2, y2]]< | box_end | > The coordinates $[x 1, y 1] /[x 2, y 2]$ denote the top-left and bottom-right point of box of queried objective. \\
\hline Example Description & <| box_start | >[[x1, y1, x2, y2], [x3, y3, x4, y4]]<| box_end | > Supporting multiple boxes for a single queried objective. \\
\hline Example Description & < | object_ref_start | >obj< | object_ref_end | >< | box_start | > [[x1, y1, x2, y2] ] < | box_end | > Detecting the 'obj' and its corresponding box. \\
\hline Example Description & <| ocr_text_start | >text< | ocr_text_end | >< | box_start | > [[x1, y1, x2, y2] ] < | box_end | > Identify the OCR results and its corresponding box. \\
\hline \multicolumn{2}{|c|}{polygons} \\
\hline & Example < | object_ref_start | >obj< | object_ref_end | >< | polygon_start | > [[[x1, y1], [x2, y2], [x3, y3]]]< | polygon_end | > Description The coordinates [ $\mathrm{x} 1, \mathrm{y} 1$ ], [ $\mathrm{x} 2, \mathrm{y} 2$ ], ... represent polygon vertices of 'obj', which arranged in clockwise order. \\
\hline & Example < | ocr_text_start | >text< | ocr_text_end | >< | polygon_start | > [[[ x1, y1], [ x2, y2], [ x3, y3]]]<| polygon_end | > Description Supporting the OCR results. \\
\hline
\end{tabular}

Table 1: Grounding Label Assembling of Keye-VL.
semantic signals in language model side, (3) Improved Generalization: The diverse and interleaved nature of the data strengthens the model's ability to reason across modalities and generalize to unseen tasks. Besides the open-source interleaved data, we also build a large-scale in-house interleaved data generation pipeline. Specifically, we focus on the two type of raw rich-text documents processing, the academic PDF data and structured knowledge data, especially the Science, Technology, Engineering, and Mathematics (STEM) data. We collect a substantial amount of academic and knowledge-based $\mathrm{PDF} /$ structured data to render the text content into plain text format and insert the corresponding images at their original positions within the text. In such a process, we conduct rigorous data protection strategies to ensure high-quality outputs. Our pipeline includes: (1) Garbled character recognition: identifying and removing garbled characters, (2) Low-resolution/broken image filtering: ensuring image quality, (3) Text-image similarity validation: ensuring semantic alignment between interleaved image-text.

\subsection*{3.1.5 Video Data}

As a short-video and live-streaming service provider, the video understanding ability is the most important point of Kwai, such as understanding the video details, generating summaries, and expressing interesting implications. To reach the goal, our video data are collected from multiple sources, including diverse open-source datasets and a large-scale high-quality in-house video data. Based on these videos, we conduct the following key pipelines to guarantee our data quality:
$\diamond$ Interleaved video-ASR: For audio signals, we currently use speech-to-text tools (e.g., Qwen2.5Omni (Xu et al. (2025a))) to recognize them, and then form a interleaved style to connect images and audio to our model.
$\diamond$ Video recaption: With (optional) ASR results, we next utilize diverse public MLLMs to generate its caption under different FPS setting, such as $0.5 / 1 / 2$.
$\diamond$ Frame-level OCR annotation: In order to ensure that our model does not miss any details in each frame, we further added a frame-level OCR task.

In addition to OCR and video captioning tasks, we have designed a series of reasoning-enhanced tasks to help the model better understand contextual relationships in short videos. These include:
$\diamond$ Frame-level re-ordering: Given a set of shuffled video frames, our model is required to predict their original chronological order, which enhances its ability to grasp temporal progression and logical flow.
$\diamond$ Multiple video matching: Provided with a group of related videos and a set of candidate videos, our model is required to identify the most contextually relevant candidate, which refines its understanding of semantic connections across different videos.
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-09.jpg?height=475&width=1617&top_left_y=219&top_left_x=228)

Figure 3: The Kwai Keye pre-training pipeline, featuring a four-stage progressive strategy: Image-Text Matching, ViT-LLM Alignment, Multi-task Pre-training, and Annealing with model merging.

\subsection*{3.2 Training Recipe}

We employ a four-stage progressive training strategy to build a powerful multi-modal foundation model with strong vision-language alignment capabilities. The training pipeline, illustrated in Figure 3, is meticulously designed to ensure that each stage has a clear and interconnected objective.

The Vision Transformer (Dosovitskiy et al. (2020)) (ViT) is initialized with weights from the siglip-so400m-patch14-384 model and undergoes continuous pre-training using the SigLIP (Zhai et al. (2023)) contrastive loss function. This stage focuses on adapting the vision encoder to our internal data distribution. We incorporate native dynamic resolution processing (akin to NaViT (Dehghani et al. (2023))), which preserves the original aspect ratio of images to the greatest extent possible. Additionally, 2D Rotary Position Embeddings (Su et al. (2024)) (RoPE) are integrated to enhance the model's extrapolation capabilities when processing images of varying resolutions.

Stage 1: cross-modal alignment: The language model is initialized from Qwen3-8B (Yang et al. (2025)). During this stage, the parameters of both the vision and language models are frozen. Training is focused on optimizing the projection MLP layer. With large-scale datasets, we establish a robust alignment between cross-modal features, laying the groundwork for the subsequent learning phase.

Stage 2: multi-task pre-training: All model parameters are unfrozen for end-to-end optimization using a diverse set of multi-task training data. The data in this stage encompasses a wide range of common vision-language tasks, including Image Captioning, Optical Character Recognition (OCR), Grounding, Visual Question Answering (VQA), and interleaved image-text data. This process significantly enhances the model's fundamental visual understanding capabilities.

Stage 3: annealing: This stage involves an annealing phase where the model is fine-tuned on a curated set of high-quality data. The primary goal is to address the issue of insufficient exposure to high-quality samples during the large-scale, broader training of Stage 2. Through optimized learning strategies and data mixtures, we further refine the model's nuanced understanding and capabilities.

Model merging: The performance of pre-trained models on downstream tasks is highly sensitive to the training data mixture, an effect that is particularly pronounced in smaller models (Li et al. (2025b)). Relying on a fixed data ratio selected based on a validation set can amplify the model's intrinsic biases, leading to discrepancies between benchmark performance and real-world application. To alleviate this, in the final phase of pre-training, we explore a homogeneous-heterogeneous merging technique. This involves averaging the weights of models that have been annealed with different data mixtures. This approach preserves the diverse capabilities of the individual models while reducing overall bias and enhancing model robustness.

\section*{4 Post-Training}

As shown in Figure 4 and Figure 5, the post-training process for Kwai Keye-VL is a meticulously designed, two-stage methodology engineered to cultivate a comprehensive suite of capabilities. The initial phase, encompassing the first two steps, is dedicated to establishing foundational performance in natural image understanding and text interaction. The subsequent stage, comprising the final three steps, focuses on progressively enhancing the model's sophisticated reasoning abilities.
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-10.jpg?height=506&width=1563&top_left_y=261&top_left_x=241)

Figure 4: No-Reasoning Training Pipeline: The process begins with a Base model, proceeds through Supervised Fine-Tuning (utilizing 70k task galaxy, 200k filtered QA pairs, and human-annotated image/video captions), and culminates in Mixed Preference Optimization with various preference data sources (400k open-source data, 10k RFT data, 90k text data, and 30k human-annotated data).
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-10.jpg?height=686&width=1591&top_left_y=996&top_left_x=238)

Figure 5: Reasoning Training Pipeline: The process consists of three key steps: CoT Cold Start (involving sampling, quality checks, and high-frequency think path selection from a data pool to create a CoT ColdStart Dataset), Mix-Mode RL (featuring Thinking Mode, Non-Thinking Mode, Auto-Thinking Mode, and Agentic Mode with Outcome and Consistency Rewards), and Iterative Alignment (implementing an RL Model with Mixed Preference Optimization, Rejection Sampling, and Preference Data Filtering based on repetition, instruction following quality, and logical scores).

The training begins with large-scale Supervised Fine-Tuning (SFT) to elevate performance across a wide array of tasks. This is followed by Mixed Preference Optimization (Wang et al. (2024b)) (MPO) to solidify model stability and efficacy in non-reasoning contexts. The third and fourth stages mark a significant leap in cognitive function, introducing Chain-of-Thought (Wei et al. (2022)) (CoT) capabilities through Cold-Start and further refining them via RL. The final stage employs iterative alignment to construct highquality preference data, which empowers the model to autonomously select the appropriate reasoning mode, thereby ensuring robust and stable performance in practical applications.

To prevent data leakage during post-training, we perform strict data deduplication by removing training samples that are highly similar to common benchmark examples, thereby ensuring fair and unbiased evaluation. Detailed information can be found in Appendix A.

\subsection*{4.1 No-Reasoning Training: Establishing Foundational Performance}

This initial phase establishes the model's core performance and stability in non-reasoning scenarios through two sequential steps (Figure 4).

\subsection*{4.1.1 Step I.1: Supervised Fine-Tuning}

The SFT data candidate pool contains over 5 million multimodal QA samples. We employ the following construction methods to balance comprehensiveness and data quality.
$\diamond$ To ensure task diversity, we utilize the proprietary TaskGalaxy (Chen et al. (2025)) framework, which categorizes data across a comprehensive system of 70,000 distinct multimodal task types.
$\diamond$ To ensure the data's challenge, MLLMs are employed to generate multiple reasoning paths for each data point. The complexity of each sample is then measured based on the correctness and length of these responses, allowing for the filtration of overly simple data.
$\diamond$ To ensure data reliability, human annotators have meticulously crafted captions for the images and videos within the training set.

The training strategy involves a dynamic learning rate. In the later phases of training, the model undergoes an annealing process at a lower learning rate. Evaluations show this annealing step contributes approximately a $1 \%$ performance improvement across both open-source and internal benchmarks.

\subsection*{4.1.2 Step I.2: Mixed Preference Optimization}

Following SFT, the model undergoes MPO to continuously refine its performance. The dataset composition includes 400,000 open-source samples, 50,000 re-constructed preference samples, 10,000 selfimprovement samples, 90,000 text-only samples, and 30,000 human-annotated samples. For open-source data, simple deduplication and filtering of existing multimodal preference data are performed, retaining 400,000 samples. The construction methods for the remaining data are as follows:
Re-constructed preference data: Datasets with ground truth answers and correct responses, such as MMRLHF (Zhang et al. (2025)) and MMPR, are collected, and open-source large models (e.g., Qwen2.5-VL 72B) are used to sample high-quality negative examples.
Reinforcement fine-tuning (RFT) data: Preference pairs targeting the SFT model's weaknesses are specifically constructed. The construction process follows two steps: (1) Based on benchmark results and human evaluation feedback, tasks where the model performs inadequately are identified, using the SFT target as the chosen results. (2) Rejection sampling outputs of the SFT model are evaluated using reward models or rule-based rewards to select low-scoring cases as rejected examples.
Text-only data: 90,000 in-house text-only preference pairs are incorporated.
Human-annotated data: Following the MM-RLHF pipeline, different responses to the same prompts are generated using both open-source MLLMs and closed-source APIs. These responses are ranked according to human scoring to create 30,000 human-annotated preference pairs. This high-quality data continuously enhances the performance of this stage.
The training strategy for this stage applies the MPO algorithm, utilizing the constructed paired preference data to optimize its overall performance in non-reasoning contexts.

\subsection*{4.2 Reasoning Training: Core Breakthrough for Complex Cognition}

This phase represents Kwai Keye-VL's most significant contribution, introducing mix-mode CoT ColdStart and RL mechanisms to substantially enhance multimodal perception, reasoning, and "think with image" capabilities for complex tasks (Figure 5).

\subsection*{4.2.1 Step II.1: CoT Cold-Start}

This crucial step initializes the model's Chain-of-Thought capabilities. Fine-tuning in this step employs a mixed dataset comprising 330,000 non-reasoning samples, 230,000 reasoning samples, 20,000 automatic reasoning samples, and 100,000 agentic reasoning samples. This strategic balance combines "LongCoT/Thinking" data targeting complex, multi-step reasoning tasks requiring logical rigor and process explainability, with "Instruct/non-thinking" data addressing everyday scenarios demanding quick, clear responses. This combination fosters structured thinking for complex problems while maintaining stylistic diversity and response flexibility for open-ended tasks. The training data consists of:

Non-Thinking data/Instruct data: This dataset mirrors the distribution from SFT and MPO stages without overlapping samples. Our experiments demonstrate that combining Instruct data with Long-CoT data yields performance gains significantly exceeding those achieved by using Long-CoT data alone.
Thinking data: This category includes over 70,000 multimodal samples designed for complex perception
and reasoning across mathematics, science, charts, and complex OCR domains. The intuition behind this data construction is to create a "high-frequency" or "commonly traversed" thinking path for each data point, ensuring accuracy in the long-CoT process. To achieve this, we employ a sophisticated construction process: 1) We use MLLMs to sample multiple CoT paths for each data point. 2) These paths undergo evaluation by another model for correctness in both steps and final results, with only correct samples retained. 3) We identify common, high-repetition thinking steps within these processes and score them based on frequency. 4) Finally, high-frequency thinking processes are identified as complete, high-quality reasoning paths, or high-scoring steps are combined to synthesize new, high-probability paths. The thinking data is supplemented with a long-CoT text dataset generated by sampling responses from another LLM across five domains: code, mathematics, science, instruction following, and reasoning.
Auto-Think data: Sourced from datasets including MMPR, MM-Eureka (Meng et al. (2025)), and OpenR1Math (Hu et al. (2025a)), this component trains the model to autonomously determine when to engage its reasoning module. For each prompt, a MLLM first analyzes complexity. Complex prompts trigger responses in a "think + answer" format, while simpler ones receive direct answers. The final training sample comprises the initial analysis, thinking process (for complex cases only), and final answer.

Agentic data: This set of 100,000 samples enables the model's "think with image" capability through coding. Our three-stage construction process creates diverse data that teaches models to manipulate images and perform calculations via code. First, from 3 million QA pairs, the Qwen 2.5 VL-72B model identifies questions where image operations (cropping, rotation, contrast enhancement) could simplify problems or improve answer quality; the model is then asked to generate corresponding analysis and code. The code will be validated in an external sandbox and the generated image will be checked by another MLLM to ensure code usability. Second, because public datasets rarely contain images requiring rotation or contrast enhancement, to counteract cropping bias, we manually construct low-contrast OCR tasks and artificially rotate images, training the model to write code for contrast enhancement or rotation operations before providing final results, ensuring the model learns more than just cropping and magnification. Third, for mathematical problems, Gemini-2.5-pro generates step-by-step thinking processes, then GPT-4o transforms complex calculations into executable code, broadening the agent's functional scope from image operations to calculation verification. Through these data, the model can perform various image operations (cropping, rotation, magnification, contrast enhancement) via code generation while simultaneously conducting or verifying complex calculations to enhance computational accuracy.
Video data: We have carefully collected a diverse set of video data, encompassing categories such as daily natural videos, movie clips, social media short videos, gaming videos, and educational videos. Among these, there are 24,000 samples labeled as Thinking data and 80,000 samples as Non-Thinking data. The annotation methods and task types can be broadly divided into three categories: (1) various task data directly obtained and filtered from open-source datasets; (2) manually annotated data covering action classification, temporal relationships, and VQA tasks; and (3) tasks constructed using rule-based approaches that focus on temporal modeling-for example, extracting six frames from a video, randomly shuffling their order, and requiring the model to predict the original sequence. These tasks collectively enhance the model's understanding of temporal relationships within videos.

Training strategy: All of the above data are combined during training. Notably, for video samples, we individually sample both 16 and 32 frames per example to participate in training, thereby improving the model's robustness to varying video frame counts. Considering that Long-CoT data primarily targets complex multi-step reasoning tasks with an emphasis on logical inference and process explainability, while Instruct data aligns more closely with everyday usage scenarios that require quick and clear responses, their combination fosters structured thinking for complex tasks while preserving stylistic diversity and flexibility for open-ended tasks. Furthermore, incorporating auto-think and agentic data into mixed training not only boosts model performance in mathematical problem-solving, logical reasoning, and visual perception but also enables flexible output strategy selection according to different user needs. This integration ultimately leads to stronger generalization capabilities and better alignment with human preferences. As a result, the model simultaneously supports the /think, /no_think, /auto_think, and /agentic_think functionalities.

\subsection*{4.2.2 Step II.2: Mix-Mode RL}

Building on the CoT cold start, this stage employs RL to further enhance the model's abilities across several key dimensions. The training data is strategically sourced to target specific capabilities:
$\diamond$ Multimodal perception: Data involving complex text recognition and object counting is used to maintain the model's perceptual acuity.
$\diamond$ Multimodal reasoning: Datasets such as MMPR and MM-Eureka are introduced to bolster the model's reasoning capabilities.
$\diamond$ Text-based mathematical reasoning: The model is challenged with difficult mathematical problems to sharpen its quantitative skills.
$\diamond$ Agentic reasoning: 47,000 samples from the DeepEyes (Zheng et al. (2025)) dataset are incorporated.
A Mix-Mode RL strategy using the GRPO (Shao et al. (2024)) algorithm is applied. The reward signal is provided by large multimodal models, which score both the correctness of the final result and the consistency between the reasoning process and the outcome. In addition, we specifically focused on using RL to enhance short video understanding.

RL for short video understanding: At this stage, our goal is to enhance the model's ability to understand short video content while ensuring broad applicability across diverse video understanding scenarios, which stands out as one of our model's most distinctive strengths. We aim to enable the model not only to comprehend short video content effectively, but also to assess the video reasonably based on that understanding. By leveraging ground-truth or annotated labels available in various short video content understanding tasks, we apply RL to improve the model's video reasoning capabilities and align its outputs with the desired value orientations.
Training procedure. The RL data for short video understanding is combined proportionally with other data in Step II.2, and the model is trained using GRPO. Compared to the model before RL training, we observe a significant improvement in its performance on short video understanding, with the model's outputs providing high-quality reasoning paths that align more closely with our expectations across all test tasks. Its assessments of video content better reflect both the judgments of our annotation teams and our intended value orientations.

\subsection*{4.2.3 Step II.3: Iterative Alignment}

The final step focuses on iterative alignment to address issues like repetitive collapse and flawed reasoning logic. This is achieved using rejection-sampling data sourced from a wide range of domains, including instruction following, OCR, mathematics, charts, counting, text-only content, safety, and cognition. The data construction process involves using the Stage II. 2 model to sample multiple responses for each prompt. These responses are then scored and ranked using a hybrid system to create paired MPO data:
$\diamond$ Rule-based scores: These metrics assess objective qualities such as repetition and instruction adherence (e.g., format validation, code type verification).
$\diamond$ Model-based scores: Employing prompt engineering, other MLLMs provide scores for more subjective cognitive aspects of the response.

The training strategy consists of multiple iterations on the candidate dataset. In each cycle, "good cases" and "bad cases" are selected to construct preference pairs, which are then used to update the model via the MPO algorithm. This iterative loop not only refines the model's output but also enhances its ability to assess problem complexity and autonomously select the most appropriate reasoning mode.

\section*{5 Training Infrastructure}

To ensure efficient and stable training of the billion-parameter model, we implement deep optimizations across three key areas: parallelization strategy, load balancing, and fault tolerance.

Optimized hybrid parallelism: We adopt a hybrid parallelization strategy combining Data Parallelism (DP) and Sequence Parallelism (SP) to scale efficiently across our large compute cluster. Our DP implementation is deeply integrated with the ZeRO (Rajbhandari et al. (2020)) optimizer. This not only reduces per-device memory pressure by sharding optimizer states, gradients, and parameters, but more critically, it enables effective computation-communication overlap. During backpropagation, gradient calculation can proceed in parallel with the gradient synchronization communication, effectively hiding communication latency and improving overall training throughput.

Dynamic load balancing: To address the severe computational load imbalance caused by variable input sizes (images/videos) in multimodal training, we implement a global greedy balancing strategy. At each global step, this strategy evaluates the FLOPs of each sample in the global batch, sorts all samples in descending order by their FLOPs, and then greedily reassigns them to the parallel group with the current lowest computational load. This mechanism dynamically flattens the load across all nodes, minimizing hardware idle time and significantly boosting overall training speed.
\begin{tabular}{|l|l|l|l|l|l|l|}
\hline Models & ImageNet-1K & ImageNet-V2 & ImageNet-A & ImageNet-R & ImageNet-S & ObjectNet \\
\hline Base (SigLIP-400M-384-14) & 83.08 & 77.34 & 82.22 & 95.78 & 74.59 & 76.99 \\
\hline + 1D interpolation & 82.02 & 75.96 & 80.92 & 94.50 & 70.74 & 67.58 \\
\hline + 1D interpolation + 2D RoPE & 82.65 & $\underline{76.80}$ & 83.26 & 95.22 & 72.59 & 78.70 \\
\hline
\end{tabular}

Table 2: Comparison of ViT variants on the ImageNet benchmarks: The highest scores are marked in bold and the second highest are underlined.

Sample-level auto-resume: Large-scale training is prone to cause frequent hardware and software failures. To counter this, we build a sample-level auto-resume mechanism. This system performs joint checkpointing of both the training state and the data I/O state. It enables a training job to automatically resume from the exact sample where it was interrupted, requiring no manual intervention. This greatly enhances training stability and resource utilization efficiency.

Post-training framework enhancements: For post-training, in addition to the strategies above, we update vLLM (Kwon et al. (2023)) to be compatible with Keye's model architecture and video inputs, enabling rapid sampling. Furthermore, we deploy multiple reward models. A random dispatch strategy is employed during the reward calculation process to reduce time overhead during the RL stage.

\section*{6 Evaluation}

\subsection*{6.1 Zero-shot Image Classification of ViT}

To validate that our continue trained native-resolution ViT is able to capture promising visual representations, we conduct a wide-used zero-shot image classification benchmark analysis. In our evaluation, we perform a comparative analysis between the base SigLIP model and its two native-resolution position embedding variants, leveraging the CLIP Benchmark ${ }^{2}$ framework with text prompt template ${ }^{3}$.

The evaluation covers six benchmark datasets: ImageNet-1K, ImageNet-V2, ImageNet-A, ImageNetR, ImageNet-S and ObjectNet, and its results are shown in Table 2. From it, we have the following observations: (1) Compared with base SigLIP model, our 1D interpolation position embedding nativeresolution model variant has slightly performance degeneration, the reason might be the interpolated 1D position encoding cannot uniquely identify the underlying 2D patch arrangement. For instance, a sequence of 196 patches may correspond to multiple distinct spatial configurations (e.g., $14 \times 14,7 \times 28$, or $28 \times 7$ ), leading to ambiguous spatial localization during feature projection. (2) With 2D RoPE modification, our ViT could clearly perceive the shape of the image, and showing competitive results with Base SigLIP performance (the best and runner-up results). We think the reason maybe our continued pretraining corpus sharing the same distribution with our MLLMs, rather than the Image-Text matching task.

\subsection*{6.2 Public Benchmarks}

In this section, we evaluate Keye-VL across various benchmarks. For general vision-language tasks, we select MMMU (Yue et al. (2024)), AI2D (Kembhavi et al. (2016)), V* (Wu \&Xie (2024)), BLINK (Fu et al. (2024)), VLMS are Blind (Rahmanzadehgervi et al. (2024)), ZeroBench (Roberts et al. (2025)), VisuLogic (Xu et al. (2025b)), RealWorldQA (X (2025)), SimpleVQA (Cheng et al. (2025)), MMStar (Chen et al. (2024a)), MMVP (Tong et al. (2024)), HallusionBench (Guan et al. (2024)) and All-Angles-Bench (Yeh et al. (2025)). For Doc and OCR tasks, we select ChartQA (Masry et al. (2022)), CharXivDQ (Wang et al. (2024d)), and OCRBench (Liu et al. (2024d)). For MATH tasks, we select MathVision (Wang et al. (2024a)), MathVista $_{\text {MINI }}$ (Lu et al. (2023)), MathVerse ${ }_{\text {vision }}$ (Zhang et al. (2024a)), OlympiadBench (He et al. (2024)), WeMath (Qiao et al. (2024)), LogicVista (Xiao et al. (2024)), and DynaMath (Zou et al. (2024)). For public Video tasks, we select Video-MME(Fu et al. (2025a)), Video-MMMU (Hu et al. (2025b)), TempCompass (Liu et al. (2024c)), LongVideoBench (Wu et al. (2024a)), and MMVU (Zhao et al. (2025)).

We compare the performance of Keye-VL in Thinking and Auto-Think mode with state-of-the-art models of a similar scale, including Qwen2.5-VL 7B, InternVL3-8B (Zhu et al. (2025)), MiMo-VL-7B-RL (Xiaomi (2025)), and proprietary models such as GPT-4o and Claude-3.7-Sonnet.

\footnotetext{
${ }^{2}$ https://github.com/LAION-AI/CLIP_benchmark
${ }^{3}$ https://colab.research.google.com/github/openai/clip/blob/master/notebooks/Prompt_Engineering_ for_ImageNet.ipynb\#scrollTo=sRqDoz1Gbsii
}
\begin{tabular}{|l|l|l|l|l|l|l|l|}
\hline Benchmark & Keye-VL 8B-Thinking & Keye-VL 8B-Auto-Think & Qwen2.5-VL 7B & InternVL3 $8 B$ & MiMo-VL 7B-RL & GPT-4o & Claude 3.7 Sonnet \\
\hline \multicolumn{8}{|c|}{General} \\
\hline $\mathrm{MMMU}_{\text {val }}$ & 71.4 & $\underline{66.8}$ & 58.6 & 62.7 & 66.7 & 70.7 & 69.8 \\
\hline AI2D & 86.7 & 85.8 & 83.9 & 85.2 & 83.5 & 82.6 & 81.4 \\
\hline V* & 69.6 & 67.9 & 79.1 & 71.2 & 81.7 & 73.9 & - \\
\hline BLINK $_{\text {val }}$ & 52.0 & 52.5 & 56.4 & 55.5 & 62.4 & 60.0 & 62.3 \\
\hline VLMs are Blind & 57.1 & $\underline{61.0}$ & 37.4 & 36.8 & 79.4 & 49.8 & 72.1 \\
\hline ZeroBench ${ }_{\text {sub }}$ & 15.2 & 11.1 & 0.0 & 0.0 & - & - & - \\
\hline VisuLogic & $\underline{25.6}$ & 21.1 & 20.0 & 26.1 & - & - & - \\
\hline RealWorldQA & 67.7 & 66.1 & 68.2 & 70.6 & - & - & - \\
\hline SimpleVQA & 41.6 & 36.9 & $\underline{41.4}$ & 35.1 & - & - & - \\
\hline MMStar & 75.5 & 72.8 & 64.9 & 68.4 & 70.8 & - & - \\
\hline MMVP & $\underline{79.0}$ & 80.3 & 78.0 & 78.3 & - & - & - \\
\hline HallusionBench & 67.0 & 57.2 & 55.7 & 49.4 & 61.9 & - & - \\
\hline All-Angles-Bench & 47.3 & 50.3 & 49.4 & 50.7 & - & - & - \\
\hline \multicolumn{8}{|c|}{Doc \& OCR} \\
\hline ChartQA & 86.3 & 72.5 & 90.2 & 89.6 & 91.7 & 86.7 & 92.2 \\
\hline CharXiv $_{\text {DQ }}$ & 77.7 & 75.2 & 73.9 & 73.6 & 86.8 & 86.5 & 89.5 \\
\hline OCRBench & 85.1 & 85.3 & 89.7 & 88.0 & 86.6 & 84.3 & 80.6 \\
\hline \multicolumn{8}{|c|}{MATH} \\
\hline MathVision & $\underline{46.0}$ & 42.4 & 26.2 & 28.8 & 60.4 & 31.2 & - \\
\hline MathVista ${ }_{\text {MINI }}$ & $\underline{80.7}$ & 75.2 & 66.8 & 70.7 & 81.5 & 63.8 & - \\
\hline MathVerse ${ }_{\text {vision }}$ & 59.8 & 40.8 & 41.2 & 32.4 & 71.5 & 49.9 & - \\
\hline OlympiadBench & $\underline{54.8}$ & 45.2 & 19.4 & 25.9 & 59.4 & 25.9 & - \\
\hline WeMath & 60.7 & 58.6 & 37.7 & 38.5 & 66.3 & 50.6 & - \\
\hline LogicVista & $\underline{54.8}$ & 50.6 & 44.5 & 43.6 & 61.4 & 54.4 & - \\
\hline DynaMath & 37.3 & 35.3 & 20.1 & 23.9 & 45.9 & 54.4 & - \\
\hline \multicolumn{8}{|c|}{Video} \\
\hline Video- $\mathrm{MME}_{\mathrm{w} / \mathrm{o} \text { sub }}$. & 67.7 & 59.7 & 65.1 & 66.3 & $\underline{67.4}$ & 71.9 & - \\
\hline Video-MMMU & 57.6 & $\underline{56.9}$ & 47.4 & 48.9 & 43.3 & - & - \\
\hline TempCompass & 71.5 & 58.2 & 68.3 & 70.8 & 68.3 & - & - \\
\hline LongVideoBench & 62.8 & 64.8 & 59.3 & 63.9 & 50.0 & - & - \\
\hline MMVU & 66.1 & 60.3 & 45.5 & 39.4 & 58.2 & - & - \\
\hline \multicolumn{8}{|c|}{Short-Video} \\
\hline CPV & 55.1 & 55.9 & 20.1 & 15.0 & 16.7 & - & - \\
\hline Aggregation & 54.3 & 55.0 & 46.4 & 52.3 & 49.0 & - & - \\
\hline Collection Order & 84.4 & $\underline{82.0}$ & 59.8 & 64.8 & 78.7 & - & - \\
\hline Pornographic & 72.0 & 70.4 & 56.1 & 57.1 & 68.3 & - & - \\
\hline High Like & 55.3 & $\underline{53.4}$ & 47.9 & 47.0 & 51.1 & - & - \\
\hline SPU & 87.1 & 84.9 & 81.3 & 75.6 & 81.9 & - & - \\
\hline
\end{tabular}

Table 3: Comparison of Keye-VL in Thinking and Auto-Think mode with other models on diverse visual-language benchmarks: The best results among open-source models are bolded and the secondbest results are underlined.

On general vision-language tasks, Keye-VL demonstrates competitive performance across most benchmarks in Thinking mode, often achieving SOTA or near SOTA results and outperforming other models overall. On the large-scale general benchmarks $\mathrm{MMMU}_{\text {val }}$ and AI2D, Keye-VL obtains scores of $71.4 \%$ and $86.7 \%$ respectively, surpassing all other models. On the more challenging ZeroBench ${ }_{\text {sub }}$ and MMVP benchmarks, Keye-VL also achieves the best performance. Furthermore, Keye-VL exhibits a lower hallucination rate, achieving an accuracy of $67 \%$ on HallusionBench. In mathematical reasoning tasks, Keye-VL significantly outperforms Qwen2.5-VL 8B and InternVL3-8B, ranking second only to MiMo-VL 7B-RL. In Auto-Think mode, Keye still achieves excellent performance: On MMMU val, AI2D, ZeroBench ${ }_{\text {sub }}$, and HallusionBench, the performance of Keye-VL in auto-think mode is second only to Keye-VL in thinking mode. On BLINK ${ }_{\text {val }}$ and VLMs are Blind, the auto-think mode even surpasses the thinking mode. On MMVP, the auto-think mode achieves state-of-the-art results. On the remaining benchmarks, the auto-think mode results in only a slight performance decline. These results demonstrate Keye-VL's ability to spontaneously select the correct thinking mode and the potential of the auto-think mode to alleviate the phenomenon of over-thinking.

In video-centric scenarios, Keye-VL demonstrates superior capabilities compared to other open-source models. Our evaluations on both public and internal benchmarks indicate that an accurate understanding of video content is one of Keye-VL's core strengths. On public video benchmarks, Keye-VL significantly outperforms other models in both modes, particularly on Video-MMMU, with an absolute improvement of $8.7 \%$ in thinking mode. On LongVideoBench, auto-think mode surpasses thinking mode by $2 \%$, this indicates that Keye-VL has sufficiently extracted video information on the input side, allowing it to obtain the correct answer without excessive reasoning.

To better evaluate the short-form video understanding capabilities of Keye-VL, we construct and opensource the Kuaishou Community Multimodal Benchmark (KC-MMBench in Table 4) ${ }^{4}$. This benchmark assesses model performance across several key dimensions of short-form video comprehension and has undergone rigorous manual inspection and data anonymization. On KC-MMBench, Keye-VL achieves an average accuracy of $68.03 \%$, substantially surpassing the second-best model, MiMo-VL 7B-RL, which attains an accuracy of $57.62 \%$. This highlights the effectiveness and application potential of Keye-VL in the domain of short-form video understanding.

\subsection*{6.3 Internal Benchmarks}

Despite extensive evaluations on a wide array of public benchmarks, these benchmarks exhibit numerous limitations that necessitate a focused effort on developing a proprietary, internal evaluation suite. The primary issues are as follows:
$\diamond$ Benchmark contamination: An unavoidable limitation of public datasets is the potential for their data to have been inadvertently or deliberately exposed during the model's training process. This phenomenon, known as benchmark contamination, can lead to exaggerated performance metrics, reducing the sensitivity of public benchmarks to subtle model improvements and thus failing to reflect the model's capabilities. To obtain a reliable assessment of a model's genuine performance, it is imperative to construct an internal evaluation benchmark that is insulated from training data contamination.
$\diamond$ Limited language coverage: The majority of public benchmarks are predominantly focused on Englishlanguage scenarios. This significantly constrains the exploration of a model's abilities within the context of native Chinese applications. English benchmarks are incapable of adequately covering or measuring the unique expressions, profound cultural contexts, and diverse local needs inherent to the Chinese language environment. Therefore, building a benchmark that can effectively evaluate a model's Chinese language capabilities is of paramount importance.
$\diamond$ Insufficient task and domain coverage: Existing general-purpose evaluation benchmarks primarily concentrate on fundamental perception and simple reasoning abilities. They fail to comprehensively cover a multi-dimensional spectrum of capabilities, including fine-grained perception, cross-modal grounding, language modeling, complex reasoning, and safety $\mathcal{E}$ robustness. Furthermore, current benchmarks lack a focus on real-world application scenarios, particularly core business needs such as multimodal understanding in short video communities. This deficiency results in an evaluation framework that cannot effectively reflect a model's performance in practical business tasks, severely hindering the assessment of its practical value and potential for deployment.
$\diamond$ Monotonous task difficulty and evaluation format: The task types and question formats (e.g., true/false, multiple-choice, fixed-answer questions) in existing benchmarks are relatively simple and uniform. They are insufficient for comprehensively measuring a model's capacity to handle complex, open-ended question answering, which more closely mirrors authentic user interactions. Constructing a more challenging evaluation benchmark that supports open-ended generative responses is better suited to simulate real-world user and business interactions, thereby enabling a more accurate assessment of the model's generalization capabilities in practical scenarios.

\subsection*{6.3.1 Design Strategies and Core Principles}

In response to the limitations inherent in existing public evaluation benchmarks and motivated by the aforementioned needs, we construct an internal evaluation benchmark set, and the full breakdown for the benchmark is detailed in Table 10. This structure is intended to fully reflect the model's performance and application potential across different sub-scenarios. While ensuring robust coverage of textual abilities, we place a special emphasis on multimodal capabilities, particularly for images and videos. In developing the evaluation benchmarks for image and video modalities, we adhere to the following core principles:
$\diamond$ Targeted capabilities and business relevance: The internal benchmark is oriented towards measuring the foundational model's ability to handle real user interaction tasks within a native Chinese context. We particularly focus on the model's performance in open-ended question answering scenarios that

\footnotetext{
${ }^{4}$ The detailed construction process of KC-MMbench is shown in Appendix B.
}
\begin{tabular}{|l|l|l|l|l|}
\hline & Task Definition & Image/Video & Input & Output \\
\hline Pornographic Comment & The task of justifying whether short video comments contain pornographic content. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=365\&width=209\&top_left_y=326\&top_left_x=720) & Please answer the question based on the video cover and extracted video information. The video information is as follows: Video Title: The wind from south of the Yangtze River blows on the birth of all things. Video ASR: You say those sleepless nights and white dreams are hazy and hypoxic... User Comment: Every time I watch her dance, I get a special feeling, so good-looking, a queenly vibe [Let's party together]. Please determine whether the comment is pornographic based on the information above. The final output should be "Yes" or "No". & No \\
\hline Collection Order & The task of determining the logical order between multiple videos with the same topic. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=115\&width=209\&top_left_y=726\&top_left_x=720) & The following is a list of information for 3 videos. It is known that the 3 videos are from the same video collection. Please sort them based on the video content. Please output your answer in the format of a list, with the video numbers in sequential order, such as: [Video 1, Video 3, Video 2]. & [Video 3, Video 2, Video 1] \\
\hline Hot Videos Aggregation & The task of determining whether multiple videos belong to the same topic. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=115\&width=207\&top_left_y=966\&top_left_x=720) & The following is a list of information for 3 videos. Please determine if the videos in the given list belong to the same topic as the first video. Each image corresponds to a screenshot of a different video. Please output your answer in the format of a list. Each item in the list represents the judgment of whether the corresponding video belongs to the same topic as the first video. The first video also needs to be included in the list. Example: Assuming there are 3 videos in the list, the output should be "Yes Yes No", which means the second video belongs to the same topic as the first video, and the third video does not belong to the same topic as the first video. & Yes Yes No \\
\hline High Like & A binary classification task to determine the rate of likes of a short video. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=273\&width=207\&top_left_y=1172\&top_left_x=721) & Please answer the question based on the video cover and extracted video information. This is a video on a short video platform. Please judge whether the video can get a high like rate on the platform based on the above video content information. The final output needs to be "Yes" or "No". If it can get a high like rate, output "Yes", otherwise output "No". & Yes \\
\hline SPU (Standard Product Unit) & The task of determining whether two items are the same product in e-commerce. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=109\&width=204\&top_left_y=1484\&top_left_x=723) & Please determine whether the two given images belong to the same product. & Yes \\
\hline CPV (Category Property Value) & The task of predicting product attributes in e-commerce. & ![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-17.jpg?height=208\&width=213\&top_left_y=1759\&top_left_x=718) & You are an e-commerce AI assistant equipped with a range of capabilities to support various e-commerce operations. There is now an attribute prediction task. You need to summarize the attributes of the product from the specified dimensions based on the main product image and information (product title and product description) provided to you. The following is the relevant information of the product whose attributes need to be predicted: Product Title: [Return shipping compensation] 25015 women's new style fashionable jacket [small grid] BY Product Description: None. Please summarize the attribute information of this product from the dimensions of 'Women's clothing for middle-aged and elderly': ['Small camisole/small sling', 'Jacket/coat'...], 'Sleeve type': ['Regular', 'Petal sleeve'...], 'Whether there is a fur collar': ..., 'Placket':..., answer in Chinese, and the final attribute summary should be output in json string format. & \{'Women's clothing for middle-aged and elderly': 'Jacket/coat', 'Sleeve type': 'Regular', 'Whether there is a fur collar': 'No', 'Placket': 'Single-breasted', 'Sleeve length': 'Long sleeve', 'Clothing length': 'Regular style'\} \\
\hline
\end{tabular}

Table 4: Kuaishou Community Multimodal Benchmark: KC-MMBench is a self-constructed short-video benchmark for MLLMs, which contains 6 task categories and 1840 instances.
require generating complete and valuable responses, in order to assess its generalization capabilities in real business contexts more accurately. In detail, the benchmark not only covers general-purpose capabilities (such as perception and reasoning) but also evaluates potential performance in business scenarios rarely addressed by current open-source benchmarks, such as multimodal understanding in short video communities. This design aims to comprehensively assess the model's applied value and ensure it can effectively support actual business requirements.
Comprehensive and fine-grained capability taxonomy: We design a multi-level capability taxonomy to facilitate the construction of diverse and fine-grained data tasks and to support the evaluation of the model's capabilities at various levels. At the macro-capability level (Layer 2), the evaluation framework covers a range of fundamental abilities, including basic visual skills, multimodal understanding tasks, and complex reasoning, while also incorporating safety and robustness. At the specific capability level (Layer 3/4), we further refine these dimensions into a comprehensive set of tasks.
\begin{tabular}{|l|l|l|l|l|l|}
\hline Subset & Capability (Level 2) & Keye-VL-8B thinking & Qwen2.5-VL-7B non-thinking & InternVL3-8B non-thinking & MiMo-VL-7B thinking \\
\hline \multirow{6}{*}{Video Subset} & Overall (Average) & 3.33 & $\underline{3.31}$ & 2.72 & 2.75 \\
\hline & Correctness & 3.34 & 3.41 & 2.87 & 3.07 \\
\hline & Comprehensiveness & 4.36 & $\underline{3.93}$ & 3.32 & 3.43 \\
\hline & Relevance & $\underline{4.83}$ & 4.85 & 4.59 & 4.43 \\
\hline & Fluency & 4.89 & 4.89 & 4.83 & 4.79 \\
\hline & Creativity & 3.75 & 3.50 & 2.75 & 2.92 \\
\hline \multirow{6}{*}{Image Subset} & Overall (Average) & 3.81 & 3.69 & 3.67 & 3.71 \\
\hline & Correctness & 4.05 & 3.82 & $\underline{3.91}$ & 3.87 \\
\hline & Comprehensiveness & 4.49 & 4.43 & 4.40 & 4.49 \\
\hline & Relevance & 4.91 & 4.87 & 4.93 & 4.74 \\
\hline & Fluency & $\underline{4.89}$ & 4.94 & 4.87 & 4.72 \\
\hline & Creativity & 3.69 & 4.06 & 3.69 & 3.81 \\
\hline
\end{tabular}

Table 5: Comparison of Keye-VL with other models on the internal benchmark: The evaluation is based on human annotations across five dimensions: correctness, comprehensiveness, relevance, fluency, and creativity. The highest scores are marked in bold and the second highest are underlined.
\begin{tabular}{|l|l|l|l|l|l|}
\hline Subset & Capability (Level 2) & Keye-VL-8B thinking & Qwen2.5-VL-7B non-thinking & InternVL3-8B non-thinking & MiMo-VL-7B thinking \\
\hline \multirow{9}{*}{Video Subset} & Overall (Average) & 3.33 & $\underline{3.31}$ & 2.72 & 2.75 \\
\hline & Visual Element Recognition & 3.89 & $\underline{3.54}$ & 3.40 & 3.46 \\
\hline & Temporal Information Understanding & 2.92 & 2.96 & 2.46 & 2.25 \\
\hline & Description Ability & $\underline{3.08}$ & 3.58 & 2.92 & 2.58 \\
\hline & Creative Ability & 3.17 & 3.50 & 1.83 & 2.67 \\
\hline & Knowledge-based QA & 2.78 & 2.00 & $\underline{2.11}$ & 2.17 \\
\hline & Reasoning Ability & 3.31 & 3.50 & 2.85 & 2.88 \\
\hline & Domain-specific Expertise & $\underline{3.36}$ & 4.40 & 3.30 & 3.09 \\
\hline & Robustness & 3.50 & 3.42 & 2.08 & 2.17 \\
\hline \multirow{6}{*}{\begin{tabular}{l}
Image \\
Subset
\end{tabular}} & Overall (Average) & 3.81 & 3.69 & 3.67 & 3.71 \\
\hline & Visual Recognition & 3.97 & 3.91 & 3.96 & 3.98 \\
\hline & Visual Understanding & $\underline{3.70}$ & 3.27 & 3.37 & 3.73 \\
\hline & Basic Description & 4.00 & 3.91 & 3.68 & 3.63 \\
\hline & Visual Storytelling & 3.63 & 3.94 & 3.75 & 3.31 \\
\hline & Multi-image Analysis & 3.15 & 2.77 & 2.69 & 2.92 \\
\hline
\end{tabular}

Table 6: Detailed capability comparison of Keye-VL with other models on the internal benchmark: The evaluation examines specific capabilities ranging from basic recognition to complex reasoning and multi-image analysis. The highest scores are marked in bold and the second highest are underlined.
$\diamond$ Authentic and diverse data coverage: To ensure the generalizability of our evaluation and mitigate the risk of overfitting, our benchmark emphasizes diversity in both videos and questions. Our data sources are authentic and reliable; the general benchmark utilizes a large volume of diverse and timely real-world image and video data. The scope of our sampled data is extensive, including various image categories (natural, text-based, artificial) and short videos with diverse frame rates, resolutions, and subjects (people, landscapes, objects, IPs), as well as complex cinematographic and motion elements.
$\diamond$ Mitigation of benchmark contamination: To build an evaluation benchmark that genuinely reflects model capabilities and is free from contamination, we have adopted several maintenance strategies. We employ methods such as timestamp verification to prevent data leakage and conduct manual, item-by-item quality checks to ensure data accuracy. Furthermore, we establish a dynamic maintenance mechanism featuring regular updates and on-demand supplementation. This involves continuously refreshing redundant data while adding new tasks that target model weaknesses, ensuring the benchmark remains current and capable of capturing the latest model advancements.
$\diamond$ Hierarchical evaluation methodology: We employ a multi-level evaluation system to comprehensively assess the model's open-ended question answering capabilities. This system first involves a FiveDimensional Scoring of model responses based on metrics of correctness, relevance, comprehensiveness, fluency, and creativity. To facilitate a holistic comparison, we then calculate a Composite Score by applying task-specific weights to these dimensions.

\subsection*{6.3.2 Evaluation Metrics and Baselines}

Input and parameters: For all experiments, input videos are uniformly sampled to select 64 frames. We benchmark Keye-VL against several state-of-the-art open-source models with comparable parameter scales: Qwen2.5-VL-7B, InternVL3-8B, and MiMo-VL-7B-RL. Each model is obtained from its official repository and deployed within our internal computing clusters. Notably, Keye and MiMo utilize a thinking mode, enabling internal reasoning steps during inference, while Qwen and Intern generate answers directly without such intermediate processes.

Evaluation data format: To conduct a rigorous evaluation, we extract 150 question-answer pairs from the video subset and 150 from the image subset of our internal benchmark. All questions are open-ended to fully exercise the models' comprehension and generative abilities, avoiding the limitations inherent in multiple-choice or fill-in-the-blank formats.

Evaluation protocol: To ensure an accurate and authentic reflection of model capabilities, we rely exclusively on human-generated evaluation rather than automated metrics. For both video and image question-answering tasks, three independent annotators score each model response based on defined criteria. The individual scores are then aggregated to produce a final score. In cases where annotator disagreement arises, a professional third-party panel is consulted to arbitrate and ensure consistency.

Five-dimensional metrics: We adopt a five-dimensional evaluation framework to assess model outputs:
$\diamond$ Correctness measures the factual accuracy of the model's response, including whether it correctly interprets visual content and whether any referenced knowledge is factually sound.
$\diamond$ Relevance assesses the degree to which the response directly addresses the user's query and remains contextually tied to the visual input.
$\diamond$ Comprehensiveness captures two related aspects: (1) For descriptive tasks, it examines the model's ability to identify and describe key visual elements, including main subjects and finer details; (2) For question-answering tasks, it evaluates whether the answer includes sufficient explanation or reasoning, rather than merely stating a result.
$\diamond$ Fluency evaluates the linguistic quality of the response, focusing on grammatical correctness, coherence, logical flow, and readability, ensuring the output is easy to understand and free of errors.
$\diamond$ Creativity applies primarily to generative or creative tasks, measuring the originality, imagination, and diversity expressed in the response.

Overall evaluation metric: To obtain a unified score that reflects task-specific priorities, we combine the five dimensions with weights dependent on the type of task as follows:
$\diamond$ For question-answering tasks, the importance hierarchy is: Correctness, Relevance, Comprehensiveness, then Fluency; Creativity is excluded given its limited relevance.
$\diamond$ For descriptive tasks, Correctness remains paramount, followed by Comprehensiveness, then Relevance, and finally Fluency; Creativity is again excluded.
$\diamond$ For creative generation tasks, the order shifts to Relevance and Creativity as primary factors, then Correctness and Comprehensiveness, with Fluency last.

To maintain a rigorous standard, if any single evaluation dimension receives a score below 3, the overall score is capped at 3 or below. This rule prevents high aggregate scores from masking critical deficiencies in any one dimension, thereby ensuring a balanced and trustworthy assessment of model performance.

\subsection*{6.3.3 Evaluation Results}

Keye-VL-8B achieves top composite scores demonstrating robust video capabilities: As shown in Table 5, Keye-VL-8B achieves the highest overall composite score of 3.33 on the video subset evaluation, demonstrating its robust and comprehensive capabilities. The model's outstanding performance is particularly reflected in the dimensions of Comprehensiveness (4.36) and Creativity (3.75), where it significantly outperforms all competing models. This highlights our model's powerful abilities in video understanding, description, question answering, and creative narration. We attribute this advantage primarily to the carefully designed data and training strategies tailored for video tasks.

The model maintains strong competitiveness across core performance dimensions: Although the model's Correctness score (3.34) is slightly lower than that of Qwen2.5-VL-7B, the margin is minimal and it remains within the top tier. Overall, the evaluation results confirm that Keye-VL-8B maintains competitive
performance on fundamental dimensions such as fluency and relevance at a level comparable to industryleading models, while establishing a distinct competitive edge in complex video tasks requiring deep reasoning and divergent thinking. Importantly, the successful application of this multidimensional scoring framework not only validates the model's leading position but also underscores the unique value of the evaluation methodology in penetrating surface-level performance metrics to directly assess core capabilities and user experience.
Keye-VL-8B effectively transfers and excels in image domain tasks: Keye-VL-8B extends its capabilities from video to the static image domain, achieving the top ranking on the image subset with a composite score of 3.81. Its core metrics, including Correctness (4.05) and Comprehensiveness (4.49), also lead the industry. This indicates the model's VQA abilities are both reliable and comprehensive. Notably, we observe an intriguing performance profile: unlike other models whose Creativity scores generally increase on image tasks, our model exhibits a inferior performance in this dimension (3.69). We interpret this not as a deficiency but as a reflection of a deliberate design focus. It further emphasizes Keye-VL-8B's is weighted toward complex dynamic relational reasoning rather than creative generation on static images.

The model shows strong foundational strengths in key video capabilities: To further dissect the model's specific competencies on video tasks, we perform a fine-grained breakdown of capability dimensions. As detailed in Table 6, Keye-VL-8B attains the highest scores among all evaluated models in three fundamental areas: Visual Element Recognition (3.89), (Prior Knowledge-Based) Question Answering (2.78), and Robustness (3.50). Additionally, it demonstrates competitive strength in Temporal Information Understanding (2.92), closely matching the leading contenders. These results strongly evidence that our model has established a solid foundation in core perceptual and cognitive pathways for video understanding, enabling more accurate and reliable parsing of objective video content. In contrast, some competitors show superior performance in reasoning, creative generation, and domain-specific knowledge dimensions that rely more heavily on large language model generation capabilities. We believe these differences reflect divergent development paths: Keye-VL-8B prioritizes ensuring correct and stable visual world understanding, thereby laying a firm groundwork for future improvements in trustworthiness and capability ceilings on more complex reasoning and interactive tasks.
The model also achieves a significant leading edge in Basic Description (4.0), Visual Recognition (3.97), and Visual Comprehension (3.70) of the image subtasks. Particularly remarkable is the model's decisive lead on the complex task of Multi-Image Analysis (3.15). We attribute this advantage directly to the model's deep understanding of correlations across multiple frames, developed through extensive training on massive short video data. This ability to handle temporal information has effectively generalized into analyzing spatial and logical relationships across multiple static images. This finding not only validates our technical approach but also reveals the unique advantage derived from video-based training, endowing the model with powerful potential for handling complex visual inputs.

\subsection*{6.3.4 Analysis of Kwai Keye-VL's Limitations}

Although our model demonstrates competitive performance across multiple benchmarks, we have identified several limitations. These limitations not only reveal areas for improvement in the current model but also guide our directions for the next phase of iterative enhancement.
1. Core visual perception capability: The primary challenge lies in the precise recognition of key visual elements in complex scenes. Specifically, the model still exhibits a non-negligible error rate in OCR, especially for Chinese characters, when processing images containing dense or stylized text. Furthermore, for fine-grained recognition tasks-such as accurately distinguishing specific species of animals or plants, identifying detailed clothing items on persons, or differentiating subtle variations of objects-the model occasionally confuses or misidentifies targets. Regarding information completeness, the model sometimes omits secondary objects or partial textual information within images, indicating that its ability to build a comprehensive understanding of the scene needs strengthening.
2. Temporal understanding in video processing: Our analysis indicates that the model exhibits instability when describing coherent temporal action sequences, particularly in distinguishing between coarsegrained and fine-grained semantic levels of actions. Additionally, the model's perception of cinematic language, such as camera movements and viewpoint shifts, remains relatively weak. It also shows potential for improvement in tasks requiring precise localization of events within video timelines, temporal ordering, tracking object changes, and motion trajectories. These shortcomings constrain the model's ability to perform deep and coherent analysis of video content.
3. Higher-order cognitive and reasoning capability: The model's reliability declines on problems that demand rigorous logical chains or mathematical calculations. In question involving specialized domain knowledge (e.g., professional disciplines, well-known intellectual properties), the model occasionally produces factual inaccuracies or omissions. Furthermore, although the model performs well in creative
generation generally, in scenarios requiring high degrees of originality or deep conceptualization, its outputs may tend toward generic or patterned responses.

\subsection*{6.4 Quantitative Results}

We provide a selection of qualitative examples illustrating the capabilities of the Kwai Keye-VL model in various aspects. Our presentation is divided into two dimensions: Modality and Thinking Mode. In Appendix C.1, we demonstrate the Kwai Keye-VL model's capabilities in pure text (Appendix C.1.1), images (Appendix C.1.2), and videos (Appendix C.1.3), including public benchmark cases and business scenario cases. In Appendix C.2, we showcase the two thinking modes of the Kwai Keye-VL model: Agentic Thinking (Appendix C.2.1) and Auto Thinking (Appendix C.2.2).

\section*{7 Discussion}

\subsection*{7.1 Mutual Enhancement between Reasoning and Non-Reasoning Data}

As discussed in Section 4.2.1, following the non-reasoning stage, we introduce the CoT-Mix dataset to cold-start Keye-VL's reasoning capabilities. During this phase, Keye-VL acquires proficiency in both the Non-Thinking and Thinking modes. We evaluate the impact of Step II. 1 (cold start with CoT-Mix data) by comparing Keye-VL's performance before and after this training stage. In the Non-Thinking and Thinking modes, Keye-VL's performance improves by $5.67 \%$ and $8.22 \%$ on MMMU, and by $2.95 \%$ and $7.97 \%$ on HallucinationBench, respectively. These results demonstrate that training on CoT-Mix data strengthens both perceptual and reasoning abilities. Furthermore, since similar non-reasoning data is also employed during Steps I. 1 and I.2, the observed performance gains in the Non-Thinking mode are primarily attributable to the inclusion of long CoT reasoning data. This suggests that the integration of long CoT data also enhances the model's capability in the Non-Thinking mode, which aligns with recent findings reported in ERNIE 4.5 ( Team (2025b)).

On MathVista, Keye-VL's accuracy increases by -1.1\% (Non-Thinking) and 7.9\% (Thinking), suggesting that the model spontaneously establishes the connection between the Thinking mode and complex logical reasoning. Based on this observation, we introduce Auto-Think mode in step II. 2 to further enhance Keye-VL's ability to select reasoning modes based on difficulty and improve overall performance.

\subsection*{7.2 Performance Gain from RL Training}

How to synchronously and stably improve the model's performance across various tasks is one of the core challenges of RL. In Step II. 2 (Section 4.2.2), we attempt to achieve this by adopting the Mix-Mode strategy. In our conception, Non-Thinking and Thinking Modes are respectively suitable for simple and complex tasks. Therefore, during the RL process, we apply Keye-VL to generate samples using a mix of both two modes to comprehensively enhance its performance across various tasks, and we incorporate the Auto-Think mode as we discussed before.

The evaluation results show that after RL training, Keye-VL achieves an average improvement of $1.44 \% / 2.17 \%$ in Non-Thinking/Thinking mode across 10 benchmarks. Its performance increase by $0 \% / 0.93 \%$ to $4.18 \% / 5.73 \%$ on 9 benchmarks, with a decrease of only $1.11 \% / 1.2 \%$ on MMMU/OCRBench. This indicates that mix-mode RL strategy achieves a comprehensive and synchronous improvement.

\subsection*{7.3 Analysis about Auto-Think Mode}
\begin{tabular}{ccccc}
\hline Benchmark & MathVista $_{\text {MINI }}$ & MMStar & HallusionBench & OCRBench \\
\hline Thinking Ratio & 0.35 & 0.34 & 0.08 & 0.00 \\
\hline
\end{tabular}

Table 7: Proportions of Keye-VL selecting the Thinking mode rather than the Non-Thinking mode across various benchmarks when operating in Auto-Think mode.

To evaluate Keye-VL's ability to spontaneously select the reasoning mode by task type and difficulty, we calculate the proportions of Keye-VL choosing the Thinking mode during Auto-Think inference across four different benchmarks (Table 7). It turns out that on the more challenging and logic-reasoning-oriented MAthVista and MMStar, Keye more frequently selects the Thinking mode. However, Non-Thinking remains the predominant mode, as the difficulty of most samples is limited. On the HallusionBench, Keye-VL selects the Thinking mode only in few instances. In these cases, Keye-VL engages in reflection to
prevent errors, as demonstrated in Appendix C.1.2. On the OCRBench, Keye-VL adopts the Non-Thinking mode for all instances.

\section*{8 Conclusion and Future Work}

In this work, we present Kwai Keye-VL, a model that achieves leading video understanding capabilities through high-quality video data construction during both pre-training and post-training phases. The mix-mode training in the post-training phase further enables Keye-VL to respond more flexibly, resulting in superior user experience. Public benchmarks and internal human evaluations demonstrate Keye-VL's strong performance in general image-text understanding, logical reasoning, and short video applications.
Nevertheless, Keye-VL has room for improvement. First, we have not specifically optimized the video encoder architecture or video encoding strategies, leaving significant potential for future enhancements. Second, Keye-VL's perceptual capabilities show only modest advantages compared to SOTA models, and its "think with image" ability remains preliminary, still lagging behind OpenAI's O3 model. Finally, our use of an additional MLLM as a direct reward model introduces limitations in reliability and usability due to the MLLM's inherent instruction-following capabilities and computational costs. Developing more reliable and efficient reward modeling strategies remains an open question for future research.

\section*{References}

Marah Abdin, Jyoti Aneja, Hany Awadalla, Ahmed Awadallah, Ammar Ahmad Awan, Nguyen Bach, Amit Bahree, Arash Bakhtiari, Jianmin Bao, Harkirat Behl, et al. Phi-3 technical report: A highly capable language model locally on your phone. arXiv preprint arXiv:2404.14219, 2024.

Shuai Bai, Keqin Chen, Xuejing Liu, Jialin Wang, Wenbin Ge, Sibo Song, Kai Dang, Peng Wang, Shijie Wang, Jun Tang, et al. Qwen2. 5-vl technical report. arXiv preprint arXiv:2502.13923, 2025.

Minwoo Byeon, Beomhee Park, Haecheon Kim, Sungjun Lee, Woonhyuk Baek, and Saehoon Kim. Coyo-700m: Image-text pair dataset. https://github. com/kakaobrain/coyo-dataset, 2022.

Soravit Changpinyo, Piyush Sharma, Nan Ding, and Radu Soricut. Conceptual 12m: Pushing web-scale image-text pre-training to recognize long-tail visual concepts. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 3558-3568, 2021.

Jiankang Chen, Tianke Zhang, Changyi Liu, Haojie Ding, Yaya Shi, Feng Cheng, Huihui Xiao, Bin Wen, Fan Yang, Tingting Gao, et al. Taskgalaxy: Scaling multi-modal instruction fine-tuning with tens of thousands vision task types. arXiv preprint arXiv:2502.09925, 2025.
Lin Chen, Jinsong Li, Xiaoyi Dong, Pan Zhang, Yuhang Zang, Zehui Chen, Haodong Duan, Jiaqi Wang, Yu Qiao, Dahua Lin, et al. Are we on the right way for evaluating large vision-language models? arXiv preprint arXiv:2403.20330, 2024a.

Tao Chen, Enwei Zhang, Yuting Gao, Ke Li, Xing Sun, Yan Zhang, Hui Li, and Rongrong Ji. Mmict: Boosting multi-modal fine-tuning with in-context examples. ACM Transactions on Multimedia Computing, Communications and Applications, 2024b.

Zhe Chen, Weiyun Wang, Yue Cao, Yangzhou Liu, Zhangwei Gao, Erfei Cui, Jinguo Zhu, Shenglong Ye, Hao Tian, Zhaoyang Liu, et al. Expanding performance boundaries of open-source multimodal models with model, data, and test-time scaling. arXiv preprint arXiv:2412.05271, 2024c.

Zhe Chen, Jiannan Wu, Wenhai Wang, Weijie Su, Guo Chen, Sen Xing, Muyan Zhong, Qinglong Zhang, Xizhou Zhu, Lewei Lu, et al. Internvl: Scaling up vision foundation models and aligning for generic visual-linguistic tasks. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 24185-24198, 2024d.

Xianfu Cheng, Wei Zhang, Shiwei Zhang, Jian Yang, Xiangyuan Guan, Xianjie Wu, Xiang Li, Ge Zhang, Jiaheng Liu, Yuying Mai, et al. Simplevqa: Multimodal factuality evaluation for multimodal large language models. arXiv preprint arXiv:2502.13059, 2025.

Mehdi Cherti, Romain Beaumont, Ross Wightman, Mitchell Wortsman, Gabriel Ilharco, Cade Gordon, Christoph Schuhmann, Ludwig Schmidt, and Jenia Jitsev. Reproducible scaling laws for contrastive language-image learning. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 2818-2829, 2023.

Mostafa Dehghani, Basil Mustafa, Josip Djolonga, Jonathan Heek, Matthias Minderer, Mathilde Caron, Andreas Steiner, Joan Puigcerver, Robert Geirhos, Ibrahim M Alabdulmohsin, et al. Patch n'pack: Navit, a vision transformer for any aspect ratio and resolution. Advances in Neural Information Processing Systems, 36:2252-2274, 2023.

Matt Deitke, Christopher Clark, Sangho Lee, Rohun Tripathi, Yue Yang, Jae Sung Park, Mohammadreza Salehi, Niklas Muennighoff, Kyle Lo, Luca Soldaini, et al. Molmo and pixmo: Open weights and open data for state-of-the-art multimodal models. arXiv preprint arXiv:2409.17146, 2024.

Harish Dattatraya Dixit, Sneha Pendharkar, Matt Beadon, Chris Mason, Tejasvi Chakravarthy, Bharath Muthiah, and Sriram Sankar. Silent data corruptions at scale. arXiv preprint arXiv:2102.11245, 2021.

Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.

FaceBook. The llama 4 herd: The beginning of a new era of natively multimodal ai innovation. https: //ai.meta.com/blog/llama-4-multimodal-intelligence/, 2025.

Qianhan Feng, Wenshuo Li, Tong Lin, and Xinghao Chen. Align-kd: Distilling cross-modal alignment knowledge for mobile vision-language model. arXiv preprint arXiv:2412.01282, 2024.

Chaoyou Fu, Yuhan Dai, Yongdong Luo, Lei Li, Shuhuai Ren, Renrui Zhang, Zihan Wang, Chenyu Zhou, Yunhang Shen, Mengdan Zhang, et al. Video-mme: The first-ever comprehensive evaluation benchmark of multi-modal llms in video analysis. In Proceedings of the Computer Vision and Pattern Recognition Conference, pp. 24108-24118, 2025a.

Chaoyou Fu, Haojia Lin, Xiong Wang, Yi-Fan Zhang, Yunhang Shen, Xiaoyu Liu, Haoyu Cao, Zuwei Long, Heting Gao, Ke Li, et al. Vita-1.5: Towards gpt-4o level real-time vision and speech interaction. arXiv preprint arXiv:2501.01957, 2025b.

Xingyu Fu, Yushi Hu, Bangzheng Li, Yu Feng, Haoyu Wang, Xudong Lin, Dan Roth, Noah A Smith, Wei-Chiu Ma, and Ranjay Krishna. Blink: Multimodal large language models can see but not perceive. In European Conference on Computer Vision, pp. 148-166. Springer, 2024.

Samir Yitzhak Gadre, Gabriel Ilharco, Alex Fang, Jonathan Hayase, Georgios Smyrnis, Thao Nguyen, Ryan Marten, Mitchell Wortsman, Dhruba Ghosh, Jieyu Zhang, et al. Datacomp: In search of the next generation of multimodal datasets. Advances in Neural Information Processing Systems, 36:27092-27112, 2023.

Aaron Grattafiori, Abhimanyu Dubey, Abhinav Jauhri, Abhinav Pandey, Abhishek Kadian, Ahmad Al-Dahle, Aiesha Letman, Akhil Mathur, Alan Schelten, Alex Vaughan, et al. The llama 3 herd of models. arXiv preprint arXiv:2407.21783, 2024.

Shuhao Gu, Jialing Zhang, Siyuan Zhou, Kevin Yu, Zhaohu Xing, Liangdong Wang, Zhou Cao, Jintao Jia, Zhuoyi Zhang, Yixuan Wang, et al. Infinity-mm: Scaling multimodal performance with large-scale and high-quality instruction data. arXiv preprint arXiv:2410.18558, 2024.

Tianrui Guan, Fuxiao Liu, Xiyang Wu, Ruiqi Xian, Zongxia Li, Xiaoyu Liu, Xijun Wang, Lichang Chen, Furong Huang, Yaser Yacoob, et al. Hallusionbench: an advanced diagnostic suite for entangled language hallucination and visual illusion in large vision-language models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 14375-14385, 2024.

Daya Guo, Dejian Yang, Haowei Zhang, Junxiao Song, Ruoyu Zhang, Runxin Xu, Qihao Zhu, Shirong Ma, Peiyi Wang, Xiao Bi, et al. Deepseek-r1: Incentivizing reasoning capability in llms via reinforcement learning. arXiv preprint arXiv:2501.12948, 2025a.

Dong Guo, Faming Wu, Feida Zhu, Fuxing Leng, Guang Shi, Haobin Chen, Haoqi Fan, Jian Wang, Jianyu Jiang, Jiawei Wang, et al. Seed1. 5-vl technical report. arXiv preprint arXiv:2505.07062, 2025b.

Kai Han, Jianyuan Guo, Yehui Tang, Wei He, Enhua Wu, and Yunhe Wang. Free video-llm: Prompt-guided visual perception for efficient training-free video llms. arXiv preprint arXiv:2410.10441, 2024.

Chaoqun He, Renjie Luo, Yuzhuo Bai, Shengding Hu, Zhen Leng Thai, Junhao Shen, Jinyi Hu, Xu Han, Yujie Huang, Yuxiang Zhang, et al. Olympiadbench: A challenging benchmark for promoting agi with olympiad-level bilingual multimodal scientific problems. arXiv preprint arXiv:2402.14008, 2024.

Jingcheng Hu, Yinmin Zhang, Qi Han, Daxin Jiang, Xiangyu Zhang, and Heung-Yeung Shum. Open-reasoner-zero: An open source approach to scaling up reinforcement learning on the base model. arXiv preprint arXiv:2503.24290,2025a.

Kairui Hu, Penghao Wu, Fanyi Pu, Wang Xiao, Yuanhan Zhang, Xiang Yue, Bo Li, and Ziwei Liu. Videommmu: Evaluating knowledge acquisition from multi-discipline professional videos. arXiv preprint arXiv:2501.13826, 2025b.

Aaron Hurst, Adam Lerer, Adam P Goucher, Adam Perelman, Aditya Ramesh, Aidan Clark, AJ Ostrow, Akila Welihinda, Alan Hayes, Alec Radford, et al. Gpt-4o system card. arXiv preprint arXiv:2410.21276, 2024.

Sahar Kazemzadeh, Vicente Ordonez, Mark Matten, and Tamara Berg. ReferItGame: Referring to objects in photographs of natural scenes. In Alessandro Moschitti, Bo Pang, and Walter Daelemans (eds.), Proceedings of the 2014 Conference on Empirical Methods in Natural Language Processing (EMNLP), pp. 787798, Doha, Qatar, October 2014. Association for Computational Linguistics. doi: 10.3115/v1/D14-1086. URL https://aclanthology.org/D14-1086.

Aniruddha Kembhavi, Mike Salvato, Eric Kolve, Minjoon Seo, Hannaneh Hajishirzi, and Ali Farhadi. A diagram is worth a dozen images. In Computer Vision-ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part IV 14, pp. 235-251. Springer, 2016.

Ranjay Krishna, Yuke Zhu, Oliver Groth, Justin Johnson, Kenji Hata, Joshua Kravitz, Stephanie Chen, Yannis Kalantidis, Li-Jia Li, David A. Shamma, Michael S. Bernstein, and Li Fei-Fei. Visual genome: Connecting language and vision using crowdsourced dense image annotations. International Journal of Computer Vision, 123:32-73, 2017. doi: 10.1007/s11263-016-0981-7. URL https: / /doi . org/10.1007/ s11263-016-0981-7.

Woosuk Kwon, Zhuohan Li, Siyuan Zhuang, Ying Sheng, Lianmin Zheng, Cody Hao Yu, Joseph E. Gonzalez, Hao Zhang, and Ion Stoica. Efficient memory management for large language model serving with pagedattention. In Proceedings of the ACM SIGOPS 29th Symposium on Operating Systems Principles, 2023.

Feng Li, Renrui Zhang, Hao Zhang, Yuanhan Zhang, Bo Li, Wei Li, Zejun Ma, and Chunyuan Li. Llava-next-interleave: Tackling multi-image, video, and 3d in large multimodal models. arXiv preprint arXiv:2407.07895, 2024.

Xiangtai Li, Tao Zhang, Yanwei Li, Haobo Yuan, Shihao Chen, Yikang Zhou, Jiahao Meng, Yueyi Sun, Shilin Xu, Lu Qi, Tianheng Cheng, Yi Lin, Zilong Huang, Wenhao Huang, Jiashi Feng, and Guang Shi. Denseworld-1m: Towards detailed dense grounded caption in the real world, 2025a. URL https://arxiv.org/abs/2506.24102.

Yuhui Li, Fangyun Wei, Jinjing Zhao, Chao Zhang, and Hongyang Zhang. Rain: Your language models can align themselves without finetuning. arXiv preprint arXiv:2309.07124, 2023.

Yunshui Li, Yiyuan Ma, Shen Yan, Chaoyi Zhang, Jing Liu, Jianqiao Lu, Ziwen Xu, Mengzhao Chen, Minrui Wang, Shiyi Zhan, et al. Model merging in pre-training of large language models. arXiv preprint arXiv:2505.12082, 2025b.

Bin Lin, Yang Ye, Bin Zhu, Jiaxi Cui, Munan Ning, Peng Jin, and Li Yuan. Video-llava: Learning united visual representation by alignment before projection. arXiv preprint arXiv:2311.10122, 2023.

Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence Zitnick. Microsoft coco: Common objects in context. In Computer vision-ECCV 2014: 13th European conference, zurich, Switzerland, September 6-12, 2014, proceedings, part v 13, pp. 740-755. Springer, 2014.

Aixin Liu, Bei Feng, Bing Xue, Bingxuan Wang, Bochao Wu, Chengda Lu, Chenggang Zhao, Chengqi Deng, Chenyu Zhang, Chong Ruan, et al. Deepseek-v3 technical report. arXiv preprint arXiv:2412.19437, 2024a.

Yangzhou Liu, Yue Cao, Zhangwei Gao, Weiyun Wang, Zhe Chen, Wenhai Wang, Hao Tian, Lewei Lu, Xizhou Zhu, Tong Lu, et al. Mminstruct: A high-quality multi-modal instruction tuning dataset with extensive diversity. Science China Information Sciences, 67(12):1-16, 2024b.

Yuanxin Liu, Shicheng Li, Yi Liu, Yuxiang Wang, Shuhuai Ren, Lei Li, Sishuo Chen, Xu Sun, and Lu Hou. Tempcompass: Do video llms really understand videos? arXiv preprint arXiv:2403.00476, 2024c.

Yuliang Liu, Zhang Li, Mingxin Huang, Biao Yang, Wenwen Yu, Chunyuan Li, Xu-Cheng Yin, Cheng-Lin Liu, Lianwen Jin, and Xiang Bai. Ocrbench: on the hidden mystery of ocr in large multimodal models. Science China Information Sciences, 67(12):220102, 2024d.

Pan Lu, Hritik Bansal, Tony Xia, Jiacheng Liu, Chunyuan Li, Hannaneh Hajishirzi, Hao Cheng, Kai-Wei Chang, Michel Galley, and Jianfeng Gao. Mathvista: Evaluating mathematical reasoning of foundation models in visual contexts. arXiv preprint arXiv:2310.02255, 2023.

Xingyu Lu, Tianke Zhang, Chang Meng, Xiaobei Wang, Jinpeng Wang, YiFan Zhang, Shisong Tang, Changyi Liu, Haojie Ding, Kaiyu Jiang, et al. Vlm as policy: Common-law content moderation framework for short video platform. arXiv preprint arXiv:2504.14904, 2025.

Gen Luo, Yiyi Zhou, Tianhe Ren, Shengxin Chen, Xiaoshuai Sun, and Rongrong Ji. Cheap and quick: Efficient vision-language instruction tuning for large language models. Advances in Neural Information Processing Systems, 36:29615-29627, 2023.

Gen Luo, Yiyi Zhou, Yuxin Zhang, Xiawu Zheng, Xiaoshuai Sun, and Rongrong Ji. Feast your eyes: Mixture-of-resolution adaptation for multimodal large language models. arXiv preprint arXiv:2403.03003, 2024a.
Run Luo, Haonan Zhang, Longze Chen, Ting-En Lin, Xiong Liu, Yuchuan Wu, Min Yang, Minzheng Wang, Pengpeng Zeng, Lianli Gao, et al. Mmevol: Empowering multimodal large language models with evol-instruct. arXiv preprint arXiv:2409.05840, 2024b.

Yongdong Luo, Xiawu Zheng, Xiao Yang, Guilin Li, Haojia Lin, Jinfa Huang, Jiayi Ji, Fei Chao, Jiebo Luo, and Rongrong Ji. Video-rag: Visually-aligned retrieval-augmented long video comprehension. arXiv preprint arXiv:2411.13093, 2024c.

Yiwei Ma, Guohai Xu, Xiaoshuai Sun, Jiayi Ji, Jie Lou, Debing Zhang, and Rongrong Ji. Mllm-selector: Necessity and diversity-driven high-value data selection for enhanced visual instruction tuning. arXiv preprint arXiv:2503.20502, 2025.

Ahmed Masry, Do Xuan Long, Jia Qing Tan, Shafiq Joty, and Enamul Hoque. Chartqa: A benchmark for question answering about charts with visual and logical reasoning. arXiv preprint arXiv:2203.10244, 2022.

Fanqing Meng, Lingxiao Du, Zongkai Liu, Zhixiang Zhou, Quanfeng Lu, Daocheng Fu, Tiancheng Han, Botian Shi, Wenhai Wang, Junjun He, et al. Mm-eureka: Exploring the frontiers of multimodal reasoning with rule-based reinforcement learning. arXiv preprint arXiv:2503.07365, 2025.

Jordan Meyer, Nick Padgett, Cullen Miller, and Laura Exline. Public domain 12m: A highly aesthetic image-text dataset with novel governance mechanisms. arXiv preprint arXiv:2410.23144, 2024.

OpenAI. Introducing openai o3 and o4-mini. https://openai.com/index/ introducing-o3-and-o4-mini/, 2025.

Runqi Qiao, Qiuna Tan, Guanting Dong, Minhui Wu, Chong Sun, Xiaoshuai Song, Zhuoma GongQue, Shanglin Lei, Zhe Wei, Miaoxuan Zhang, et al. We-math: Does your large multimodal model achieve human-like mathematical reasoning? arXiv preprint arXiv:2407.01284, 2024.

Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pp. 8748-8763. PmLR, 2021.

Pooyan Rahmanzadehgervi, Logan Bolton, Mohammad Reza Taesiri, and Anh Totti Nguyen. Vision language models are blind. In Proceedings of the Asian Conference on Computer Vision, pp. 18-34, 2024.

Samyam Rajbhandari, Jeff Rasley, Olatunji Ruwase, and Yuxiong He. Zero: Memory optimizations toward training trillion parameter models. In SC20: International Conference for High Performance Computing, Networking, Storage and Analysis, pp. 1-16. IEEE, 2020.
Miao Rang, Zhenni Bi, Chuanjian Liu, Yehui Tang, Kai Han, and Yunhe Wang. Eve: Efficient multimodal vision language models with elastic visual experts. arXiv preprint arXiv:2501.04322, 2025.

Jonathan Roberts, Mohammad Reza Taesiri, Ansh Sharma, Akash Gupta, Samuel Roberts, Ioana Croitoru, Simion-Vlad Bogolin, Jialu Tang, Florian Langer, Vyas Raina, et al. Zerobench: An impossible visual benchmark for contemporary large multimodal models. arXiv preprint arXiv:2502.09696, 2025.

Christoph Schuhmann, Romain Beaumont, Richard Vencu, Cade Gordon, Ross Wightman, Mehdi Cherti, Theo Coombes, Aarush Katta, Clayton Mullis, Mitchell Wortsman, et al. Laion-5b: An open large-scale dataset for training next generation image-text models. Advances in neural information processing systems, 35:25278-25294, 2022.

ByteDance Seed, Jiaze Chen, Tiantian Fan, Xin Liu, Lingjun Liu, Zhiqi Lin, Mingxuan Wang, Chengyi Wang, Xiangpeng Wei, Wenyuan Xu, et al. Seed1. 5-thinking: Advancing superb reasoning models with reinforcement learning. arXiv preprint arXiv:2504.13914, 2025.
Zhihong Shao, Peiyi Wang, Qihao Zhu, Runxin Xu, Junxiao Song, Xiao Bi, Haowei Zhang, Mingchuan Zhang, YK Li, Y Wu, et al. Deepseekmath: Pushing the limits of mathematical reasoning in open language models. arXiv preprint arXiv:2402.03300, 2024.

Yunhang Shen, Chaoyou Fu, Shaoqi Dong, Xiong Wang, Peixian Chen, Mengdan Zhang, Haoyu Cao, Ke Li, Xiawu Zheng, Yan Zhang, et al. Long-vita: Scaling large multi-modal models to 1 million tokens with leading short-context accuray. arXiv preprint arXiv:2502.05177, 2025.

Jianlin Su, Murtadha Ahmed, Yu Lu, Shengfeng Pan, Wen Bo, and Yunfeng Liu. Roformer: Enhanced transformer with rotary position embedding. Neurocomputing, 568:127063, 2024.
Zhaochen Su, Linjie Li, Mingyang Song, Yunzhuo Hao, Zhengyuan Yang, Jun Zhang, Guanjie Chen, Jiawei Gu, Juntao Li, Xiaoye Qu, et al. Openthinkimg: Learning to think with images via visual tool reinforcement learning. arXiv preprint arXiv:2505.08617, 2025.

BAAI RoboBrain Team. Robobrain 2.0 technical report. arXiv preprint arXiv:TODO, 2025a.
Baidu ERNIE Team. Ernie 4.5 technical report, 2025b.
Gemini Team, Rohan Anil, Sebastian Borgeaud, Jean-Baptiste Alayrac, Jiahui Yu, Radu Soricut, Johan Schalkwyk, Andrew M Dai, Anja Hauth, Katie Millican, et al. Gemini: a family of highly capable multimodal models. arXiv preprint arXiv:2312.11805, 2023.

Gemini Robotics Team, Saminda Abeyruwan, Joshua Ainslie, Jean-Baptiste Alayrac, Montserrat Gonzalez Arenas, Travis Armstrong, Ashwin Balakrishna, Robert Baruch, Maria Bauza, Michiel Blokzijl, et al. Gemini robotics: Bringing ai into the physical world. arXiv preprint arXiv:2503.20020, 2025a.

Kimi Team, Angang Du, Bohong Yin, Bowei Xing, Bowen Qu, Bowen Wang, Cheng Chen, Chenlin Zhang, Chenzhuang Du, Chu Wei, et al. Kimi-vl technical report. arXiv preprint arXiv:2504.07491, 2025b.

Shengbang Tong, Zhuang Liu, Yuexiang Zhai, Yi Ma, Yann LeCun, and Saining Xie. Eyes wide shut? exploring the visual shortcomings of multimodal llms. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 9568-9578, 2024.
Dmitry Ustalov, Nikita Pavlichenko, Sergey Koshelev, Daniil Likhobaba, and Alisa Smirnova. Toloka visual question answering benchmark. arXiv preprint arXiv:2309.16511, 2023.

Ke Wang, Junting Pan, Weikang Shi, Zimu Lu, Houxing Ren, Aojun Zhou, Mingjie Zhan, and Hongsheng Li. Measuring multimodal mathematical reasoning with math-vision dataset. Advances in Neural Information Processing Systems, 37:95095-95169, 2024a.
Weiyun Wang, Zhe Chen, Wenhai Wang, Yue Cao, Yangzhou Liu, Zhangwei Gao, Jinguo Zhu, Xizhou Zhu, Lewei Lu, Yu Qiao, and Jifeng Dai. Enhancing the reasoning ability of multimodal large language models via mixed preference optimization. arXiv preprint arXiv:2411.10442, 2024b.

Xinlong Wang, Xiaosong Zhang, Zhengxiong Luo, Quan Sun, Yufeng Cui, Jinsheng Wang, Fan Zhang, Yueze Wang, Zhen Li, Qiying Yu, et al. Emu3: Next-token prediction is all you need. arXiv preprint arXiv:2409.18869, 2024c.

Zirui Wang, Mengzhou Xia, Luxi He, Howard Chen, Yitao Liu, Richard Zhu, Kaiqu Liang, Xindi Wu, Haotian Liu, Sadhika Malladi, et al. Charxiv: Charting gaps in realistic chart understanding in multimodal llms. Advances in Neural Information Processing Systems, 37:113569-113697, 2024d.

Jason Wei, Xuezhi Wang, Dale Schuurmans, Maarten Bosma, Fei Xia, Ed Chi, Quoc V Le, Denny Zhou, et al. Chain-of-thought prompting elicits reasoning in large language models. Advances in neural information processing systems, 35:24824-24837, 2022.

Haoning Wu, Dongxu Li, Bei Chen, and Junnan Li. Longvideobench: A benchmark for long-context interleaved video-language understanding. Advances in Neural Information Processing Systems, 37: 28828-28857, 2024a.

Mingrui Wu, Xinyue Cai, Jiayi Ji, Jiale Li, Oucheng Huang, Gen Luo, Hao Fei, Guannan Jiang, Xiaoshuai Sun, and Rongrong Ji. Controlmllm: Training-free visual prompt learning for multimodal large language models. Advances in Neural Information Processing Systems, 37:45206-45234, 2024b.

Penghao Wu and Saining Xie. V*: Guided visual search as a core mechanism in multimodal 1 lms . In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 13084-13094, 2024.
X. Real world qa benchmark. https://huggingface.co/datasets/xai-org/RealworldQA, 2025.

Yijia Xiao, Edward Sun, Tianyu Liu, and Wei Wang. Logicvista: Multimodal llm logical reasoning benchmark in visual contexts. arXiv preprint arXiv:2407.04973, 2024.

LLM-Core-Team Xiaomi. Mimo-vl technical report, 2025. URL https://arxiv.org/abs/2506.03569.
Jin Xu, Zhifang Guo, Jinzheng He, Hangrui Hu, Ting He, Shuai Bai, Keqin Chen, Jialin Wang, Yang Fan, Kai Dang, et al. Qwen2. 5-omni technical report. arXiv preprint arXiv:2503.20215, 2025a.

Weiye Xu, Jiahao Wang, Weiyun Wang, Zhe Chen, Wengang Zhou, Aijun Yang, Lewei Lu, Houqiang Li, Xiaohua Wang, Xizhou Zhu, et al. Visulogic: A benchmark for evaluating visual reasoning in multi-modal large language models. arXiv preprint arXiv:2504.15279, 2025b.

An Yang, Anfeng Li, Baosong Yang, Beichen Zhang, Binyuan Hui, Bo Zheng, Bowen Yu, Chang Gao, Chengen Huang, Chenxu Lv, et al. Qwen3 technical report. arXiv preprint arXiv:2505.09388, 2025.

Yuan Yao, Tianyu Yu, Ao Zhang, Chongyi Wang, Junbo Cui, Hongji Zhu, Tianchi Cai, Haoyu Li, Weilin Zhao, Zhihui He, Qianyu Chen, Huarong Zhou, Zhensheng Zou, Haoye Zhang, Shengding Hu, Zhi Zheng, Jie Zhou, Jie Cai, Xu Han, Guoyang Zeng, Dahai Li, Zhiyuan Liu, and Maosong Sun. Minicpm-v: A gpt-4v level mllm on your phone. arXiv preprint arXiv:2408.01800, 2024. URL https: //arxiv.org/abs/2408.01800.

Chun-Hsiao Yeh, Chenyu Wang, Shengbang Tong, Ta-Ying Cheng, Ruoyu Wang, Tianzhe Chu, Yuexiang Zhai, Yubei Chen, Shenghua Gao, and Yi Ma. Seeing from another perspective: Evaluating multi-view understanding in mllms. arXiv preprint arXiv:2504.15280, 2025.

Liping Yuan, Jiawei Wang, Haomiao Sun, Yuchen Zhang, and Yuan Lin. Tarsier2: Advancing large vision-language models from detailed video description to comprehensive video understanding, 2025. URL https://arxiv.org/abs/2501.07888.

Xiang Yue, Yuansheng Ni, Kai Zhang, Tianyu Zheng, Ruoqi Liu, Ge Zhang, Samuel Stevens, Dongfu Jiang, Weiming Ren, Yuxuan Sun, et al. Mmmu: A massive multi-discipline multimodal understanding and reasoning benchmark for expert agi. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 9556-9567, 2024.

Xiaohua Zhai, Basil Mustafa, Alexander Kolesnikov, and Lucas Beyer. Sigmoid loss for language image pre-training, 2023.
Renrui Zhang, Dongzhi Jiang, Yichi Zhang, Haokun Lin, Ziyu Guo, Pengshuo Qiu, Aojun Zhou, Pan Lu, Kai-Wei Chang, Yu Qiao, et al. Mathverse: Does your multi-modal 1 lm truly see the diagrams in visual math problems? In European Conference on Computer Vision, pp. 169-186. Springer, 2024a.

Xiaofeng Zhang, Yihao Quan, Chaochen Gu, Chen Shen, Xiaosong Yuan, Shaotian Yan, Hao Cheng, Kaijie Wu, and Jieping Ye. Seeing clearly by layer two: Enhancing attention heads to alleviate hallucination in lvlms. arXiv preprint arXiv:2411.09968, 2024b.

Yi-Fan Zhang, Tao Yu, Haochen Tian, Chaoyou Fu, Peiyan Li, Jianshu Zeng, Wulin Xie, Yang Shi, Huanyu Zhang, Junkang Wu, et al. Mm-rlhf: The next step forward in multimodal llm alignment. arXiv preprint arXiv:2502.10391, 2025.

Yilun Zhao, Haowei Zhang, Lujing Xie, Tongyan Hu, Guo Gan, Yitao Long, Zhiyuan Hu, Weiyuan Chen, Chuhan Li, Zhijian Xu, et al. Mmvu: Measuring expert-level multi-discipline video understanding. In Proceedings of the Computer Vision and Pattern Recognition Conference, pp. 8475-8489, 2025.

Ziwei Zheng, Michael Yang, Jack Hong, Chenxiao Zhao, Guohai Xu, Le Yang, Chao Shen, and Xing Yu. Deepeyes: Incentivizing" thinking with images" via reinforcement learning. arXiv preprint arXiv:2505.14362, 2025.

Guorui Zhou, Jiaxin Deng, Jinghao Zhang, Kuo Cai, Lejian Ren, Qiang Luo, Qianqian Wang, Qigen Hu, Rui Huang, Shiyao Wang, et al. Onerec technical report. arXiv preprint arXiv:2506.13695, 2025.

Jinguo Zhu, Weiyun Wang, Zhe Chen, Zhaoyang Liu, Shenglong Ye, Lixin Gu, Hao Tian, Yuchen Duan, Weijie Su, Jie Shao, et al. Internvl3: Exploring advanced training and test-time recipes for open-source multimodal models. arXiv preprint arXiv:2504.10479, 2025.

Chengke Zou, Xingang Guo, Rui Yang, Junyu Zhang, Bin Hu, and Huan Zhang. Dynamath: A dynamic visual benchmark for evaluating mathematical reasoning robustness of vision language models. arXiv preprint arXiv:2411.00836, 2024.

\section*{A Strategies for Data Decontamination}

\section*{A. 1 Pre-training}

To prevent benchmark leakage (i.e., the model memorizes evaluation examples), we conduct rigorous data decontamination strategies to avoid the data leakage problem. In pre-training, considering the large-scale data volume, we utilize simple-yet-effective pHash\&minHash techniques to check our data corpus. We apply a decontamination process based on pHash and minHash. First, we utilize pHash to generate a 64-bit binary string, then convert its ones' ID location list to build minHash index via 128-bit permutation hashing to group them. Next, the same buckets' candidate pairs are verified using Jaccard similarity ( $>0.95$ ) to identify complex variations like partial crops or local edits. Considering that some training samples have multiple images, but as long as one image is repeated with the benchmark, we will also filter out the sample from our training data. In Table 8, we have listed some open-source datasets that may have data leaks.
\begin{tabular}{|l|l|l|l|l|l|l|}
\hline Data Source & MMBench & MMMU & AI2D & MMStar & MathVista & OCRbench \\
\hline Infinity Onevision ( Gu et al. (2024)) & 3459 & 331 & 902 & 3056 & 2827 & 826 \\
\hline MMInstruct-QA ( Liu et al. (2024b)) & 1349 & 17 & 0 & 265 & 0 & 1 \\
\hline MMEvol ( Luo et al. (2024b)) & 1842 & 140 & 645 & 1041 & 750 & 380 \\
\hline
\end{tabular}

Table 8: The number of duplicated pre-training samples with some datasets/benchmarks.

\section*{A. 2 Post Training}

In post-training, we remove any training image-question pair whose visual and textual embeddings are highly similar to instances in our evaluation benchmarks. For each candidate image-question pair in the training corpus, we compute CLIP-based cosine similarities against all evaluation benchmarks using both visual and textual encoders. Pairs exceeding similarity thresholds of 0.98 (image) and 0.50 (text) to any benchmark sample are excluded, preserving semantic diversity while systematically removing evaluation look-alikes.

This decontamination protocol is applied across 29 comprehensive benchmarks spanning five critical evaluation domains: Visual Mathematics \& Reasoning, General Multimodal Understanding, Chart/Diagram Interpretation, OCR-Centric \& Document Tasks, and Robustness \& Hallucination Diagnostics. The dual-threshold approach effectively safeguards evaluation integrity across all domains while maintaining the original training corpus, ensuring no meaningful reduction in model knowledge coverage.
Meanwhile, Table 9 lists datasets containing a substantial number of samples exceeding the defined thresholds relative to our evaluation benchmarks. Researchers should exercise caution when employing these datasets during model training to avoid potential data leakage.
\begin{tabular}{lrrrrrr}
\hline Dataset & MMBench & MMMU & AI2D & MMStar & MathVista & OCRBench \\
\hline MM-Eureka (Meng et al. (2025)) & 2,127 & 0 & 1 & 596 & 616 & 110 \\
MMPR (Wang et al. (2024b)) & 10,947 & 0 & 1,186 & 2,009 & 3,143 & 1,044 \\
\hline
\end{tabular}

Table 9: The number of duplicated post-training samples between various datasets.

\section*{B Construction of KC-MMbench}

The benchmark comprises the following tasks, designed to evaluate short-form video understanding from various perspectives:
- Standard Product Unit (SPU): This task involves comparative analysis. We randomly sample information from different products within the same commercial category. The model is then tasked with determining whether two given pieces of product information refer to the same underlying product.
- Category Property Value (CPV): We collect data across various product dimensions (e.g., color, style, material). For each dimension, we construct multiple-choice questions using attributes that are semantically close, testing the model's ability to make fine-grained distinctions.
- Hot Videos Aggregation: Short videos are collected based on real-world events. A set of videos is constructed through random sampling. One video is designated as an anchor, and the task is to identify which of the other videos in the set belong to the same event as the anchor.
- Collection Order: This task uses a collection of topically related short videos published from the same user as input. The objective is to determine the correct logical or chronological sequence of the video content.
- High-Like Video Classification: We collect data on the number of "likes" a video receives within a specific timeframe after being uploaded. Using a predetermined threshold, we classify videos into two categories: "high-like" and "low-like".
- Pornographic Comment: Leveraging historical data from both automated online moderation models and human reviewers, we randomly sample video information along with associated user comments. This data is then used to formulate a binary classification task to determine whether a given comment contains pornographic content.

All datasets sampled for the tasks described above have undergone a thorough manual review process. During this stage, we filter out data with low video quality and verify the correctness of the corresponding ground-truth answers. To protect user privacy, we have meticulously anonymized the data. All personally identifiable information, such as usernames, locations, and timestamps, has been removed from the textual data. For visual data, any sensitive information appearing in images has been blurred to prevent potential information leakage. The final evaluation benchmark is based exclusively on this manually verified and fully anonymized dataset.

Table 10: Capability Taxonomy for the Internal General Benchmark.
\begin{tabular}{|l|l|l|l|}
\hline Level 1 & Level 2 & Level 3 & Level 4 \\
\hline \multirow{10}{*}{General Benchmark} & Visual Element Recognition & \begin{tabular}{l}
Object Recognition \\
Action Recognition \\
Attribute Recognition \\
Spatial Recognition \\
Temporal Recognition \\
Location Recognition \\
Scene/Plot Recognition \\
OCR Problems \\
Counting Problems
\end{tabular} & \\
\hline & \multirow{4}{*}{Temporal Information Understanding} & Camera Motion Changes & Camera Movement Shooting Angle Changes \\
\hline & & Subject Dynamic Perception & \begin{tabular}{l}
Motion Direction Change \\
Motion Speed Change Motion Process Perception
\end{tabular} \\
\hline & & State Transition Perception & Size/Shape/Color/Light Changes Comprehensive Changes (Duration) \\
\hline & & Event Temporal Perception & Event Time Localization Event Sequence Rearrangement \\
\hline & Descriptive Capability & \begin{tabular}{l}
Video Specific Moment Description \\
Video Specific Object Description \\
Video Specific Sub-scene Description \\
Video Dense Captioning \\
Summarization of Video \\
Abstractive Summarization
\end{tabular} & \begin{tabular}{l}
(Generate description for each event) (Major scenes, events, themes) \\
(For the entire video)
\end{tabular} \\
\hline & \multirow{3}{*}{Creative Capability} & Basic Writing & (Email, Essay, Story, Novel) \\
\hline & & \begin{tabular}{l}
Copywriting / Slogan \\
Poetry \\
Brainstorming
\end{tabular} & \\
\hline & & Multi-turn Conversation & \begin{tabular}{l}
Conversational Ability \\
Interactional Intent Understanding Contextual Understanding Answer Suggestion and Correction
\end{tabular} \\
\hline & Knowledge-based Q\&A (Prior Knowledge) & Common Sense Q\&A & \begin{tabular}{l}
Object Grounding and Naming \\
Flora/Fauna, Celebrity, Food, Landmark, IP Recognition General Life Knowledge
\end{tabular} \\
\hline
\end{tabular}

Table 10 continued from previous page
\begin{tabular}{|l|l|l|l|}
\hline Level 1 & Level 2 & Level 3 & Level 4 \\
\hline \multirow{5}{*}{} & & \begin{tabular}{l}
Music/Video Appreciation \\
Sports/Gaming Information
\end{tabular} & \\
\hline & Reasoning Capability & \begin{tabular}{l}
Descriptive Reasoning \\
Explanatory Reasoning \\
Comparative Reasoning \\
Predictive Reasoning \\
Counterfactual Reasoning \\
Spatial Relationship Judgment \\
Causal Relationship Judgment
\end{tabular} & \\
\hline & Domain-Specific Capability & \begin{tabular}{l}
Chart and Graph Analysis \\
Academic Problem Solving \\
Scientific Experiment Analysis \\
Remote Sensing Video Understanding \\
Medical Video Understanding
\end{tabular} & \\
\hline & Safety & \begin{tabular}{l}
Traditional Safety \\
Instruction Attacks
\end{tabular} & \\
\hline & Robustness & \begin{tabular}{l}
Video Noise Tests (e.g., detail blur) \\
Prompt Robustness (typos, unclear intent) \\
Robustness to varied bitrate, resolution, clarity \\
Video-Text Irrelevance
\end{tabular} & \\
\hline
\end{tabular}

\section*{C Case Study}

\section*{C. 1 Modality}

\section*{C.1.1 Pure Text Case}

Prompts: "What movie does this emoji describe?
Model Responses: <think> The user now needs to determine which movie the emoji \& 4 describes. First, analyze the meaning of each emoji: © is a bride, $X$ is a dagger, and is a fist.
Then, recall movies that involve a female protagonist in a wedding scene, armed with weapons, and fighting. For example, "Kill Bill: Volume 1" is a Quentin Tarantino film where the main character, Beatrix Kiddo (the Bride), is a bride at her wedding, then seeks revenge, using weapons like a dagger for combat. The elements of the bride, dagger, and fist (representing fighting) all match. Therefore, it should be this movie. </think>
<answer> To determine the movie corresponding to this emoji, it is necessary to analyze the meaning of the emojis and match the plot elements: (bride): Indicates a wedding scene; $\not \subset$ (dagger): Represents a weapon; (fist): Symbolizes fighting or combat.
Combining these elements, the movie "Kill Bill: Volume 1" fits the plot: the protagonist Beatrix Kiddo (the Bride) is a bride at her wedding, then embarks on a revenge journey, using weapons (such as a dagger) for combat, with the core elements of "bride + weapon + fighting" perfectly matching.
Kill Bill: Volume 1 </answer>
Prompts: "What movie does this emoji describe? 图;
Model Responses: <think> The user now needs to determine which movie the emoji " \& (;) describes. First, analyze each element: a woman ( $\odot$ ), a heart ( $V$ ), waves ( $)$, and a demon ( 0 ). Recall movies with similar elements, such as "The Shape of Water" (The Shape of Water). In the movie, a mute female cleaner falls in love with a mysterious aquatic creature (demon-like?), and the scenes include water and emotional elements. Therefore, it is necessary to confirm the elements corresponding to each part: the female protagonist, the heart symbolizing love, the waves representing the aquatic creature's environment, and the demon-like creature. Thus, the movie is "The Shape of Water."</think>
<answer> To determine the movie corresponding to the emoji " O2)", it is necessary to analyze the symbolic meaning of each element: ©: Represents a female character; $\odot$ : Symbolizes love or emotional connection; $\mathbb{E}$ : Represents water or an aquatic environment; © : Can be associated with a mysterious, otherworldly creature (such as an aquatic monster). Combining these elements, the movie "The Shape of Water" (The Shape of Water) fits well: the story revolves around a mute female cleaner who develops a profound emotional bond with a captured aquatic creature, set against a backdrop filled with water and mysterious creatures.
The Shape of Water </answer>
Figure 6: An example of Kwai Keye-VL successfully deducing the corresponding movie name based on the input emojis. We directly input the emojis in text form and Kwai Keye-VL can accurately understand the meaning of emojis, which is crucial for understanding content on media platforms.

\section*{C.1.2 Image Cases}

\section*{Image Case I: Subway Map Puzzle}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-34.jpg?height=777&width=777&top_left_y=508&top_left_x=668)

Prompts: Count the one-colored routes that go from D to A. Answer with a number in curly brackets e.g. 3
Model Responses: <think>The current task for the user is to count the number of single-colored paths from D to A. First, it is necessary to identify the color of each path and then analyze the paths for each color separately to see if they connect D to A.
First, observe the colors of the paths in the image: purple, brown, blue, orange, red, green. Then, check if the paths for each color connect D to A .
Purple path: Check if the purple path connects D to A . Trace the purple path to see if it reaches A . It appears that the purple path does not connect D to A , so it is excluded.
Brown path: Trace the brown path to see if it connects D to A . The brown path seems to connect D to A , so this is one.
Blue path: ...
Orange path: ...
Red path: ...
Green path: ...
Now, check carefully again. Could there be other colors? For example, are there any other colors? Check the colors in the image again: purple, brown, blue, orange, red, green, and possibly others? No, the colors in the image should be these. Then, confirm again if the brown path is the only one? Or are there any others?
Look carefully again, could there be other paths? For example, are there any other colors? Oh, maybe I missed something. Check again:

Oh, maybe there are no other colors that connect D to A, so the count is 1?</think>
<answer>To determine the number of single-colored paths from D to A, it is necessary to analyze the connectivity of each colored path one by one:
Purple path: Does not connect D to A, excluded. Brown path: Continuously connects D to A, meeting the condition. Blue path: Does not connect D to A, excluded. Orange path: Does not connect D to A, excluded. Red path: Does not connect D to A, excluded. Green path: Does not connect D to A, excluded. After analyzing each color, only the brown path meets the condition of "single-colored path from D to A."
1
</answer>

Figure 7: An example demonstrating Kwai Keye-VL's reflective ability learned from the RL process. Kwai Keye-VL checks the correctness of the results multiple times before providing the final answer, avoiding misinterpretation of complex images.

Input Image
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-35.jpg?height=812&width=812&top_left_y=402&top_left_x=645)

Prompts: You are an e-commerce AI assistant with multiple capabilities related to e-commerce operations.
There is a current attribute prediction task where you need to summarize the attributes of a product from specified dimensions based on the main product image and information provided (product title and product description).
Below is the relevant information for the product whose attributes need to be predicted: Product Title: New Season Fashionable Lace Hooded Jacket + High Waist Skirt Two-Piece Set LK2501761 Product Description: New Season Fashionable Lace Hooded Jacket + High Waist Skirt Two-Piece Set LK2501761
Please summarize the product's attribute information from the dimensions of 'Style': ['Skirt Set', 'Strap', 'Fake Two-Piece', 'Pants Set'], 'Clothing Fit': ['Straight', 'Slim', 'Loose', 'Cape Style', 'Batwing', 'Poncho'], 'Collar Type': ['Square Collar', 'Diagonal Collar', 'Scarf Collar', 'Detachable Hood', 'Peter Pan Collar', 'Half-Open Collar', 'Double-Layer Collar', 'Hooded', 'Pile Collar', 'Stand Collar', 'Off-Shoulder', 'Round Collar', 'Half-High Collar', 'High Collar', 'Ruffle Collar', 'V-Neck', 'Polo Collar', 'Sailor Collar', 'Suit Collar', 'None', 'Turn-Down Collar'], 'Sleeve Length': ['Sleeveless', 'Short Sleeve', 'Five-Quarter Sleeve', 'Seven-Quarter Sleeve', 'Nine-Quarter Sleeve', 'Long Sleeve'], 'Applicable Season': ['Spring', 'Summer', 'Autumn', 'Winter', 'All Seasons'] in Chinese. The final attribute summary should be output in JSON string format. Note: 1. If multiple attribute values can be extracted under one attribute dimension, organize them in a list; if only one, do not use a list. 2. If no attributes can be extracted under a specific attribute dimension, do not output that dimension's result. 3. Directly output the final summarized JSON string without additional analysis.

Model Responses: \{"Style": "Skirt Set", "Clothing Fit": "Slim", "Collar Type": "Hooded", "Sleeve Length": "Long Sleeve", "Applicable Season": "Spring"\}

Figure 8: An example demonstrating Kwai Keye-VL's application in an e-commerce scenario: Kwai Keye-VL accurately identifies the attributes of a product based on the input product image.

\section*{C.1.3 Video Cases}

\section*{Video Case I: Comment Understanding}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-36.jpg?height=510&width=804&top_left_y=439&top_left_x=649)

Prompts: You are an expert in understanding video comments. Please analyze and explain why this comment was rated as a top comment based on the video frames [video] and text. Video content information: [Video title: [Testing if Dada protects food? \#Dog \#DundunDada] Video ASR: [Yeah. I Already finished eating. I So cold.] Video content OCR information: [Testing Dada. Protects food or not I Eat I Give]] Comment content: [Dada: She can't bite me, right [Oh]];

Model Responses: Reason Analysis: The reason this comment was rated as a top comment is due to its creativity and humor. The comment uses personification, posing the question "She can't bite me, right" from the perspective of the dog "Dada," creating a clever contrast. In typical food protection tests, people worry about dogs biting to protect their food, but the comment reverses this thinking, making the dog worry about the owner biting it. This reversal breaks conventional thinking and creates a humorous effect. Additionally, the comment vividly showcases the cute interaction between the pet and the owner, aligning with the video's theme of testing food protection. It precisely captures the fun aspect of the video, resonating with the audience and eliciting a smile. The language is concise yet vivid, making it a top comment.

Figure 9: An example demonstrating Kwai Keye-VL's understanding of short video comments: Kwai Keye-VL accurately identifies the commenter imitating a pet talking to its owner, creating a contrast and humorous effect.

\section*{Video Case II: Physical Problem Reasoning}

Input Video
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-37.jpg?height=138&width=1391&top_left_y=436&top_left_x=358)

Prompts: Which of the following statements best explains the observed difference in the durations of swinging, and predicts the expected effect if the middle plate were replaced with a plate made of brass?
A. The middle plate stops sooner because the magnetic field exerts a direct mechanical force opposing its motion; replacing it with brass would cause it to stop even sooner due to increased magnetic interactions.
B. The middle plate stops sooner due to magnetic hysteresis losses as it moves through the magnetic field; replacing it with brass would cause it to stop later because brass is less susceptible to hysteresis losses.
C. The middle plate stops sooner due to eddy current damping opposing its motion; replacing it with brass, which has higher electrical resistivity, would cause it to swing shorter because larger eddy currents would be induced, leading to more damping.
D. The middle plate stops sooner because the magnetic field exerts a direct mechanical force opposing its motion; replacing it with brass would cause it to stop later due to decreased magnetic interactions.
E. The middle plate stops sooner due to eddy current damping opposing its motion; replacing it with brass, which has higher electrical resistivity, would cause it to swing longer because smaller eddy currents would be induced, leading to less damping.

Model Responses: <think> Got it, let's try to work through this problem. First, the question is about why the middle plate stops sooner when swinging, and what happens if we replace it with brass. The options mention eddy currents, magnetic hysteresis, direct mechanical force, resistivity, etc.
First, I need to remember what the setup is. The video shows a pendulum with different metal plates swinging through a magnetic field. The key here is ...
The problem mentions "the middle plate" stops sooner. Let's assume there are ...
Now, the options: Let's go through them.
Option A: Direct mechanical force. No, magnetic force on the plate isn't direct mechanical; it's due to induced currents. So A is out.

Option E: Eddy current damping. Correct mechanism. Middle plate stops sooner due to eddy currents. Then, brass has higher resistivity than, say, copper. Higher resistivity means smaller eddy currents (since eddy current is proportional to $1 /$ resistivity, other factors constant). So smaller eddy currents mean less damping, so the plate swings longer. That matches E.
Let me confirm: The induced current depends on the resistance of the material. For a given induced emf, current $\mathrm{I}=$ emf/R. So if R is higher, then I is smaller. ... That's option E.</think>
<answer>To determine the best explanation for the observed difference in swinging durations and the effect of replacing the middle plate with brass, we analyze the phenomenon using principles of electromagnetism, specifically eddy current damping:
\#\#\# **Key Observations and Analysis**
The setup involves metal plates ...
\#\#\# **Evaluation of Options**
- **Option $\mathrm{A}^{* *}$ : Incorrect...
- **Option $\mathrm{E}^{* *}$ : Correct. The middle plate stops sooner due to eddy current damping (larger eddy currents in lower-resistivity material). Brass has higher electrical resistivity than the original material (e.g., copper or aluminum), so smaller eddy currents are induced, reducing damping. This results in the brass plate swinging longer.
**Final Answer** E-/answer>

Figure 10: An example demonstrating how Kwai Keye-VL solves complex physics problems based on video content. Kwai Keye-VL accurately extracts the objects and setup from the video, analyzes each option using physics knowledge, checks them one by one, and finally confirms the correct answer.

\section*{C. 2 Thinking Mode}

\section*{C.2.1 Agentic Thinking Case}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-38.jpg?height=1548&width=1677&top_left_y=411&top_left_x=235)

Figure 11: An example of Kwai Keye-VL writing code to extract the relevant parts from high-resolution images and successfully solves the counting problem. In the agentic thinking mode, Kwai Keye-VL can independently write code to edit input images or call tools.

\section*{C.2.2 Auto Thinking Cases}

\section*{No-Thinking Case}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-39.jpg?height=546&width=686&top_left_y=438&top_left_x=688)

\section*{Prompts: Find x .}

Model Responses: <analysis>This question requires finding the value of x using the Pythagorean theorem, which is a straightforward calculation given the right triangle. Therefore, /no_think mode is more appropriate.</analysis>
The given image depicts a right triangle with one leg measuring 2 units, the hypotenuse measuring 5 units, and the other leg labeled as $x$. To find the value of $x$, we can use the Pythagorean theorem, which states that in a right triangle, the square of the length of the hypotenuse (c) is equal to the sum of the squares of the lengths of the other two sides ( $a$ and $b$ ):
$$
c^{2}=a^{2}+b^{2}
$$

In this case, the hypotenuse $c$ is 5 , one leg $a$ is 2 , and the other leg $b$ is $x$. Plugging these values into the theorem, we get:
$$
5^{2}=2^{2}+x^{2}
$$

Simplifying the equation:
$$
25=4+x^{2}
$$

Subtract 4 from both sides to isolate $x^{2}$ :
$$
21=x^{2}
$$

Taking the square root of both sides to solve for $x$ :
$$
x=\sqrt{21}
$$

Thus, the value of $x$ is $\sqrt{21}$.
Figure 12: An example demonstrating Kwai Keye-VL analyzing the difficulty of a problem and choosing the Non-Thinking mode to solve a simple geometry problem.

\section*{Thinking Case}
![](https://cdn.mathpix.com/cropped/2025_07_17_765499039fe318804acbg-40.jpg?height=512&width=729&top_left_y=349&top_left_x=692)

Prompts: Find x .
Model Responses: <analysis> This question involves calculating the area of a shaded region formed by two semicircles, where specific geometric properties and relationships between the semicircles and the chord are given. This requires understanding the geometry and performing calculations to determine the area. Therefore, /think mode is more appropriate.</analysis>
<think>The problem that users need to solve now is the area of the shaded region. First, it is necessary to understand the composition of the figure: the shaded region should be the area of the larger semicircle minus the area of the smaller semicircle, or the area of the larger semicircle minus the area of the smaller semicircle plus the area of the smaller semicircle? No, looking at the figure, the shaded region is the part of the larger semicircle excluding the smaller semicircle, so it is the area of the larger semicircle minus the area of the smaller semicircle. Then, it is necessary to find the radii of the larger and smaller semicircles.
First, let the radius of the larger semicircle be R and the radius of the smaller semicircle be r . Since CD is parallel to $A B$ and $C D$ touches the smaller semicircle, the distance from the center $O$ of the larger semicircle to $C D$ is equal to the radius r of the smaller semicircle. The length of CD is 4 , so in the right triangle formed by half of CD , the radius R of the larger semicircle, and the distance r , according to the Pythagorean theorem, we have $(\mathrm{CD} / 2)^{2}+\mathrm{r}^{2}=\mathrm{R}^{2}$, which is $2^{2}+\mathrm{r}^{2}=\mathrm{R}^{2}$, i.e., $4+\mathrm{r}^{2}=\mathrm{R}^{2}$.
Then, the area of the shaded region is the area of the larger semicircle minus the area of the smaller semicircle, which is $\frac{1}{2} \pi R^{2}-\frac{1}{2} \pi r^{2}=\frac{1}{2} \pi\left(R^{2}-r^{2}\right)$. From the previous equation, $R^{2}-r^{2}=4$, so the area is $\frac{1}{2} \pi \times 4=2 \pi$. Thus, the area of the shaded region is $2 \pi .</$ think $>$
<answer> To solve this problem, we can analyze the area of the shaded region by combining the Pythagorean theorem and the formula for the area of a circle.
Step 1: Define Variables and Analyze Geometric Relationships Let the radius of the larger semicircle be ( R ) and the radius of the smaller semicircle be ( $r$ ). Observing the figure, we know that the chord ( $C D$ ) is parallel to the diameter ( AB ) of the larger semicircle and is tangent to the smaller semicircle. Therefore, the distance from the center ( $O$ ) of the larger semicircle to ( $C D$ ) is equal to the radius ( $r$ ) of the smaller semicircle.
The length of ( CD ) is 4 , so half of ( CD ) is ( $\frac{\mathrm{CD}}{2}=2$ ). At this point, a right triangle can be formed: one leg is ( $\frac{C D}{2}=2$ ), the other leg is the distance from the center ( $O$ ) to ( $C D$ ) (i.e., ( $r$ )), and the hypotenuse is the radius ( $R$ ) of the larger semicircle.
Step 2: Use the Pythagorean Theorem to Find ( $R^{2}-r^{2}$ ) According to the Pythagorean theorem, in the right triangle, we have: $\left[\left(\frac{C D}{2}\right)^{2}+r^{2}=R^{2}\right]$ Substituting ( $\frac{C D}{2}=2$ ), we get: $\left[2^{2}+r^{2}=R^{2} \Longrightarrow 4+r^{2}=R^{2} \Longrightarrow R^{2}-r^{2}=4\right]$
Step 3: Calculate the Area of the Shaded Region The area of the shaded region is equal to the area of the larger semicircle minus the area of the smaller semicircle. According to the formula for the area of a circle ( $S=\pi r^{2}$ ), the area of the semicircle is ( $\frac{1}{2} \pi r^{2}$ ). Therefore: [ Area of the shaded region $=\frac{1}{2} \pi R^{2}-\frac{1}{2} \pi r^{2}=\frac{1}{2} \pi\left(R^{2}-r^{2}\right)$ ] Substituting ( $R^{2}-r^{2}=4$ ), we get: [ Area of the shaded region $=\frac{1}{2} \pi \times 4=2 \pi$ ]
In conclusion, the area of the shaded region is $(2 \pi)$. </answer>
Figure 13: An example demonstrating Kwai Keye-VL choosing the Thinking mode to solve a hard geometry problem.

\section*{D Authors (Alphabetical order)}

Core Contributors: Biao Yang, Bin Wen, Changyi Liu, Chenglong Chu, Chengru Song, Chongling Rao, Chuan Yi, Da Li, Dunju Zang, Fan Yang, Guorui Zhou, Hao Peng, Haojie Ding, Jiaming Huang, Jiangxia Cao, Jiankang Chen, Jingyun Hua, Jin Ouyang, Kaibing Chen, Kaiyu Jiang, Kaiyu Tang, Kun Gai, Shengnan Zhang, Siyang Mao, Sui Huang, Tianke Zhang, Tingting Gao, Wei Chen, Wei Yuan, Xiangyu Wu, Xiao Hu, Xingyu Lu, Yang Zhou, Yi-Fan Zhang, Yiping Yang, Yulong Chen, Zhenhua Wu, Zhenyu Li, Zhixin Ling, Ziming Li.
Contributors: Dehua Ma, Di Xu, Haixuan Gao, Hang Li, Jiawei Guo, Jing Wang, Lejian Ren, Muhao Wei, Qianqian Wang, Qigen Hu, Shiyao Wang, Tao Yu, Xinchen Luo, Yan Li, Yiming Liang, Yuhang Hu, Zeyi Lu, Zhuoran Yang, Zixing Zhang.