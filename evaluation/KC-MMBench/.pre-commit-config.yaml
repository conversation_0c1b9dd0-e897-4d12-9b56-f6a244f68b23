exclude: |
  (?x)^(
      scripts/|
      assets/|
      vlmeval/config.py |
      vlmeval/dataset/utils/wemath.py |
      vlmeval/dataset/Omnidocbench/ |
      vlmeval/dataset/utils/megabench/ |
      vlmeval/dataset/utils/vgrpbench/ |
      vlmeval/dataset/utils/chartmimic/ |
      vlmeval/vlm/ola/ |
      vlmeval/vlm/ursa/ |
      vlmeval/vlm/ovis/ |
      vlmeval/dataset/utils/mme_reasoning.py
  )
repos:
  - repo: https://github.com/PyCQA/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args:
          [
            "--max-line-length=120",
            "--ignore=F401,F403,F405,E402,E722,E741,W503,E231,E702",
          ]
        exclude: ^configs/
  - repo: https://github.com/pre-commit/mirrors-yapf
    rev: v0.30.0
    hooks:
      - id: yapf
        args: ["--style={column_limit=120}"]
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.1.0
    hooks:
      - id: trailing-whitespace
      - id: check-yaml
      - id: end-of-file-fixer
      - id: requirements-txt-fixer
      - id: check-merge-conflict
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: mixed-line-ending
        args: ["--fix=lf"]
