accelerate==0.34.2
bert-score==0.3.13
byted-wandb==0.13.72
datasets==2.21.0
einops==0.8.0
evaluate==0.4.3
fastapi==0.115.0
flash_attn
ftfy==6.2.3
markdown2==2.5.0
ninja==1.11.1.1
nltk==3.9.1
numpy==1.26.4
omegaconf==2.3.0
openai==0.28
opencv-python-headless==4.10.0.84
packaging==24.1
pandas==2.2.2
peft==0.5.0
prettytable==3.11.0
protobuf==3.20.3
pyarrow==15.0.0
pydantic==1.10.14
qwen_vl_utils
requests==2.32.3
rouge-score==0.1.2
scikit-image==0.24.0
scikit-learn==1.5.2
sentencepiece==0.1.97
timm==0.6.7
tokenizers>=0.13.3
torchmetrics
transformers==4.45.2
uvicorn==0.30.6
