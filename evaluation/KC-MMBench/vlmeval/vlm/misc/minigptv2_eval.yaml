model:
  arch: minigpt_v2
  model_type: pretrain
  max_txt_len: 160
  end_sym: "</s>"
  low_resource: True
  prompt_template: '[INST] {} [/INST]'
  ckpt: "please set this value to the path of pretrained checkpoint"
  lora_r: 64
  lora_alpha: 16

  # vit encoder
  image_size: 448
  drop_path_rate: 0
  use_grad_checkpoint: False
  vit_precision: "fp16"
  freeze_vit: True

  # generation configs
  prompt: ""

  # LLM
  llama_model: "please set this value to the path of llama2-chat-7b"

datasets:
  cc_sbu_align:
    vis_processor:
      train:
        name: "blip2_image_eval"
        image_size: 448
    text_processor:
      train:
        name: "blip_caption"

run:
  task: image_text_pretrain
